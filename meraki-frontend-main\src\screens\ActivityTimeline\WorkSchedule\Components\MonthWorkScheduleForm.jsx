import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  MenuItem,
  Box
} from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import Input from 'components/Input';
import SelectField from 'components/SelectField';
import { UserActions, GeneralActions } from 'slices/actions';
import { GeneralSelector, UserSelector } from 'selectors';
import { SCHEDULE_TEMPLATES, SCHEDULE_TYPES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';
import AdvancedWorkScheduleForm from './AdvancedWorkScheduleForm';

const MonthWorkScheduleForm = ({ open, onClose, selectedUser, selectedDate }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));
  const [currentSelectedUser, setCurrentSelectedUser] = useState(selectedUser);
  const [showAdvancedForm, setShowAdvancedForm] = useState(false);

  useEffect(() => {
    if (success) {
      toast.success(`Work schedule updated successfully!`, {
        position: "top-right",
        autoClose: 3000,
        closeOnClick: true,
      });
      onClose();
      // Reset success state to prevent multiple alerts
      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));
    }
  }, [success, onClose, dispatch]);

  useEffect(() => {
    setCurrentSelectedUser(selectedUser);
  }, [selectedUser]);

  // Helper function to format date for input field
  const formatDateForInput = (dateValue) => {
    if (!dateValue) {
      return new Date().toISOString().split('T')[0];
    }
    if (typeof dateValue === 'string') {
      return dateValue.split('T')[0];
    }
    return new Date(dateValue).toISOString().split('T')[0];
  };

  const formik = useFormik({
    initialValues: {
      selectedUserId: currentSelectedUser?._id || (users.length > 0 ? users[0]._id : ''),
      scheduleTemplate: currentSelectedUser?.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate,
      shiftStart: formatDateForInput(selectedDate || currentSelectedUser?.workSchedule?.shiftStart),
      shiftEnd: formatDateForInput(selectedDate || currentSelectedUser?.workSchedule?.shiftEnd),
      startTime: currentSelectedUser?.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime,
      endTime: currentSelectedUser?.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime,
      minimumHours: currentSelectedUser?.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours,
      workPlan: ''
    },
    enableReinitialize: true,
    onSubmit: (values) => {
      handleSubmit(values);
    }
  });

  const handleSubmit = (values) => {
    const targetUser = users.find(u => u._id === values.selectedUserId);
    if (!targetUser) {
      toast.error('Please select a valid user');
      return;
    }

    const params = {
      id: targetUser._id,
      workSchedule: {
        scheduleTemplate: values.scheduleTemplate,
        shiftStart: values.shiftStart,
        shiftEnd: values.shiftEnd,
        startTime: values.startTime,
        endTime: values.endTime,
        minimumHours: parseFloat(values.minimumHours)
      }
    };

    dispatch(UserActions.updateUser(params));
  };

  // Helper function to calculate hours between two times
  const calculateHours = (startTime, endTime) => {
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);

    let startMinutes = (startHour * 60) + startMin;
    let endMinutes = (endHour * 60) + endMin;

    // Handle overnight shifts (night shift)
    if (endMinutes <= startMinutes) {
      endMinutes += (24 * 60); // Add 24 hours
    }

    const diffMinutes = endMinutes - startMinutes;
    return (diffMinutes / 60).toFixed(2);
  };

  const handleWorkScheduleChange = (field, value) => {
    formik.setFieldValue(field, value);

    // If user selection changes, update the form with that user's current schedule
    if (field === 'selectedUserId') {
      const selectedUser = users.find(u => u._id === value);
      if (selectedUser) {
        setCurrentSelectedUser(selectedUser);
        formik.setFieldValue('scheduleTemplate', selectedUser.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate);
        formik.setFieldValue('startTime', selectedUser.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime);
        formik.setFieldValue('endTime', selectedUser.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime);
        formik.setFieldValue('minimumHours', selectedUser.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours);
      }
    }

    // Auto-calculate hours when start or end time changes
    if (field === 'startTime' || field === 'endTime') {
      const startTime = field === 'startTime' ? value : formik.values.startTime;
      const endTime = field === 'endTime' ? value : formik.values.endTime;

      if (startTime && endTime) {
        const calculatedHours = calculateHours(startTime, endTime);
        formik.setFieldValue('minimumHours', calculatedHours);
      }
    }

    // Auto-suggest schedule template based on time
    if (field === 'startTime') {
      const hour = parseInt(value.split(':')[0], 10);
      if (hour >= 22 || hour < 6) {
        formik.setFieldValue('scheduleTemplate', 'night_shift');
      } else {
        formik.setFieldValue('scheduleTemplate', 'day_shift');
      }
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">
          Month Work Schedule
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Date: {selectedDate}
        </Typography>
        {currentSelectedUser?.workSchedule && (
          <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 1 }}>
            Current ({currentSelectedUser.name}): {currentSelectedUser.workSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift'}
            ({currentSelectedUser.workSchedule.startTime}-{currentSelectedUser.workSchedule.endTime},
            {currentSelectedUser.workSchedule.minimumHours}h min)
          </Typography>
        )}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              {/* User Selection Dropdown */}
              <Grid item xs={12}>
                <SelectField
                  label="Select User"
                  name="selectedUserId"
                  value={formik.values.selectedUserId}
                  onChange={(e) => handleWorkScheduleChange('selectedUserId', e.target.value)}
                  required
                >
                  {users.map((user) => (
                    <MenuItem key={user._id} value={user._id}>
                      {user.name} - {user.designation?.name || user.role || 'No Role'}
                      {user.workSchedule && (
                        user.workSchedule.startTime !== '09:00' ||
                        user.workSchedule.endTime !== '17:30' ||
                        user.workSchedule.scheduleTemplate !== 'day_shift'
                      ) && (
                        <Typography component="span" variant="caption" color="primary" sx={{ ml: 1 }}>
                          ({user.workSchedule.startTime}-{user.workSchedule.endTime})
                        </Typography>
                      )}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <SelectField
                  label="Schedule Template"
                  name="scheduleTemplate"
                  value={formik.values.scheduleTemplate}
                  onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}
                  required
                >
                  {SCHEDULE_TEMPLATES.map((template) => (
                    <MenuItem key={template.value} value={template.value}>
                      {template.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Input
                  label="Minimum Hours"
                  name="minimumHours"
                  type="number"
                  step="0.1"
                  value={formik.values.minimumHours}
                  onChange={(e) => handleWorkScheduleChange('minimumHours', e.target.value)}
                  required
                  helperText={
                    formik.values.startTime && formik.values.endTime ? `Calculated: ${calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Enter start and end time to auto-calculate'
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Input
                  label="Shift Start Date"
                  name="shiftStart"
                  type="date"
                  value={formik.values.shiftStart}
                  onChange={(e) => handleWorkScheduleChange('shiftStart', e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Input
                  label="Shift End Date"
                  name="shiftEnd"
                  type="date"
                  value={formik.values.shiftEnd}
                  onChange={(e) => handleWorkScheduleChange('shiftEnd', e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <SelectField
                  label="Start Time"
                  name="startTime"
                  value={formik.values.startTime}
                  onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}
                  required
                >
                  {TIME_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <SelectField
                  label="End Time"
                  name="endTime"
                  value={formik.values.endTime}
                  onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}
                  required
                >
                  {TIME_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              <Grid item xs={12}>
                <Input
                  label="Work Plan or Notes"
                  name="workPlan"
                  multiline
                  rows={4}
                  value={formik.values.workPlan}
                  onChange={(e) => handleWorkScheduleChange('workPlan', e.target.value)}
                  placeholder="Enter work plan, goals, or any specific notes for this month..."
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{
                  p: 2,
                  bgcolor: 'info.light',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'info.main',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
          <Typography variant="body2" color="info.dark">
  <strong>Note:</strong> This updates the user&rsquo;s default schedule.
  For specific dates/times, use Advanced Scheduling.
</Typography>

                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => setShowAdvancedForm(true)}
                  >
                    Advanced
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancel
        </Button>
        <Button 
          onClick={formik.handleSubmit} 
          variant="contained" 
          color="primary"
        >
          Save Schedule
        </Button>
      </DialogActions>

      {/* Advanced Work Schedule Form */}
      <AdvancedWorkScheduleForm
        open={showAdvancedForm}
        onClose={() => setShowAdvancedForm(false)}
        selectedUser={currentSelectedUser}
        selectedDate={selectedDate}
        scheduleType="daily"
      />
    </Dialog>
  );
};

MonthWorkScheduleForm.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedUser: PropTypes.object,
  selectedDate: PropTypes.string
};

export default MonthWorkScheduleForm;
