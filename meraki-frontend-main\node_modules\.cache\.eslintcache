[{"E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\index.js": "1", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\reportWebVitals.js": "2", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\store.js": "3", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\routes.js": "4", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\theme.js": "5", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\reducers.js": "6", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\index.js": "7", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Backgroundprovider.jsx": "8", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Create.js": "9", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Form.js": "10", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\MainLayout.js": "11", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\AuthLayout.js": "12", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\Form.js": "13", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\Form.js": "14", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Auth\\Login.js": "15", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\Form.js": "16", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\Form.js": "17", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PermissionRoute.js": "18", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveApproval\\Approval.jsx": "19", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\Form.js": "20", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\config\\permissionConfig.js": "21", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\permission.js": "22", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveConfiguration\\LeaveConfiguration.jsx": "23", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveReport\\LeaveReport.jsx": "24", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Tasklist.jsx": "25", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveCalendar\\LeaveCalendar.jsx": "26", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductList.jsx": "27", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TaskHistoryAdmin.jsx": "28", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductTimesheet.jsx": "29", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\NotFound.jsx": "30", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductOverview.jsx": "31", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\AccessDenied.jsx": "32", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Client\\Client.jsx": "33", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\ProductListStaff.jsx": "34", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\TaskListWithNote.jsx": "35", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\GeneralSlice.js": "36", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AuthSlice.js": "37", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\UserSlice.js": "38", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AttendanceSlice.js": "39", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DesignationSlice.js": "40", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DepartmentSlice.js": "41", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ExpensesSlice.js": "42", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\LeaveSlice.js": "43", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\TimelineSlice.js": "44", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ProductSlice.js": "45", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ClientSlice.js": "46", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SprintSlice.js": "47", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SettingSlice.js": "48", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\UserSprintPage.jsx": "49", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintTasksPage.jsx": "50", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintPage.jsx": "51", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\index.js": "52", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\index.js": "53", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\index.js": "54", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\index.js": "55", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\index.js": "56", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\index.js": "57", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\index.js": "58", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\index.js": "59", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AuthSaga.js": "60", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\UserSaga.js": "61", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DepartmentSaga.js": "62", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DesignationSaga.js": "63", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AttendanceSaga.js": "64", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ExpenseSaga.js": "65", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\LeaveSaga.js": "66", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\TimelineSaga.js": "67", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ActivitySaga.js": "68", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ProductSaga.js": "69", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ClientSaga.js": "70", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SprintSaga.js": "71", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SettingSaga.js": "72", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\index.js": "73", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Setting\\index.js": "74", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\index.js": "75", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\index.jsx": "76", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\actions.js": "77", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PageTitle.js": "78", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\role.js": "79", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Permission.jsx": "80", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Input.js": "81", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\SelectField.js": "82", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\constants\\menus.js": "83", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\menuGenerator.js": "84", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\permissionLogger.js": "85", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\can.js": "86", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\FormSkeleton.js": "87", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\sort.js": "88", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\leaveConst.js": "89", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SettingSelector.js": "90", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\TimelineSelector.js": "91", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\FloatingButton.js": "92", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\AccountSetting.js": "93", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\BasicInformation.js": "94", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\MenuForm.js": "95", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductAdd.jsx": "96", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SprintSelector.js": "97", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TimesheetFilters.jsx": "98", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\WeeklyPicker.jsx": "99", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\DateRangeSelector.jsx": "100", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ProductSelector.js": "101", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\CustomMenu.js": "102", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\ListSkeleton.js": "103", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Charts.js": "104", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widgets.js": "105", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\DialogConfirm.js": "106", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\convertion.js": "107", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\expenseStatus.js": "108", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\UserLeaveInfo.jsx": "109", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\LeaveCalender.jsx": "110", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Filter.js": "111", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskFilterUser.jsx": "112", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\AddMembers.jsx": "113", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskHeader.jsx": "114", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeader.jsx": "115", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskInfoComponent.jsx": "116", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeaderFilterStaff.jsx": "117", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\Note.jsx": "118", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintList.jsx": "119", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintForm.jsx": "120", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\GeneralSelector.js": "121", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\UserSelector.js": "122", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DesignationSelector.js": "123", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AuthSelector.js": "124", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DepartmentSelector.js": "125", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ExpensesSelector.js": "126", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AttendanceSelector.js": "127", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ClientSelector.js": "128", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\LeaveSelector.js": "129", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\countries.js": "130", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\TimelineNew.jsx": "131", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SettingService.js": "132", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SprintService.js": "133", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\TimelineService.js": "134", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ActivityService.js": "135", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ProductService.js": "136", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\EmployeeList.js": "137", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\AttendanceList.js": "138", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\AccountSetting.js": "139", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\ExpensesList.js": "140", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\BasicInformation.js": "141", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\index.js": "142", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\apiConfig.js": "143", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\BasicInformation.js": "144", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Leave.js": "145", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Attendance.js": "146", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\AccountSetting.js": "147", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\constant\\ProductConts.js": "148", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widget.js": "149", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Activity.js": "150", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\FormLeavePop.jsx": "151", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\DayPicker.jsx": "152", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\MonthPicker.jsx": "153", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\api.js": "154", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AuthService.js": "155", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DepartmentService.js": "156", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\UserService.js": "157", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AttendanceService.js": "158", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DesignationService.js": "159", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ExpensesService.js": "160", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\LeaveService.js": "161", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\gender.js": "162", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ClientService.js": "163", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\WeekView.jsx": "164", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\MonthView.jsx": "165", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\DayView.jsx": "166", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TodayGoal.jsx": "167", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\BreakReasone.jsx": "168", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\EarlyLate.jsx": "169", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\AttendanceBarChart.jsx": "170", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\ProductivityChart.jsx": "171", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\WorkHoursStatus.jsx": "172", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\OverLimitBreak.jsx": "173", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TimelineRequest.jsx": "174", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\App.js": "175", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ActivitySlice.js": "176", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ActivitySelector.js": "177", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\LoadingScreen.js": "178", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\WorkSchedule.jsx": "179", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TaskRequest\\TaskRequest.jsx": "180", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TimeRequest\\TimeRequest.jsx": "181", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\Overview.jsx": "182", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\AdminDashboard.js": "183", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\UserDashboard.js": "184", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\DayPicker.jsx": "185", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\WeeklyPicker.jsx": "186", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\MonthPicker.jsx": "187", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\MonthlyWorkReport.jsx": "188", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\DayWorkReport.jsx": "189", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\WeekWorkReport.jsx": "190", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\MonthPicker.jsx": "191", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\DayPicker.jsx": "192", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkSchedule.jsx": "193", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\WeekWorkSchedule.jsx": "194", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkSchedule.jsx": "195", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\WeeklyPicker.jsx": "196", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\workSchedule.js": "197", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\WorkScheduleService.js": "198", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkScheduleForm.jsx": "199", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkScheduleForm.jsx": "200", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\AdvancedWorkScheduleForm.jsx": "201", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\workScheduleUtils.js": "202"}, {"size": 850, "mtime": 1749628571403, "results": "203", "hashOfConfig": "204"}, {"size": 375, "mtime": 1747124732125, "results": "205", "hashOfConfig": "204"}, {"size": 1749, "mtime": 1748931717673, "results": "206", "hashOfConfig": "204"}, {"size": 16086, "mtime": 1749883708571, "results": "207", "hashOfConfig": "204"}, {"size": 3742, "mtime": 1747124732306, "results": "208", "hashOfConfig": "204"}, {"size": 2423, "mtime": 1748931717657, "results": "209", "hashOfConfig": "204"}, {"size": 1813, "mtime": 1748931717579, "results": "210", "hashOfConfig": "204"}, {"size": 8531, "mtime": 1748931673699, "results": "211", "hashOfConfig": "204"}, {"size": 5702, "mtime": 1749628571544, "results": "212", "hashOfConfig": "204"}, {"size": 4922, "mtime": 1747124732253, "results": "213", "hashOfConfig": "204"}, {"size": 11847, "mtime": 1748931717566, "results": "214", "hashOfConfig": "204"}, {"size": 1496, "mtime": 1748407262947, "results": "215", "hashOfConfig": "204"}, {"size": 4099, "mtime": 1748522688462, "results": "216", "hashOfConfig": "204"}, {"size": 4448, "mtime": 1748522688462, "results": "217", "hashOfConfig": "204"}, {"size": 6835, "mtime": 1748931700850, "results": "218", "hashOfConfig": "204"}, {"size": 5567, "mtime": 1747124732157, "results": "219", "hashOfConfig": "204"}, {"size": 8416, "mtime": 1748522688462, "results": "220", "hashOfConfig": "204"}, {"size": 3309, "mtime": 1748931717558, "results": "221", "hashOfConfig": "204"}, {"size": 5376, "mtime": 1748577220729, "results": "222", "hashOfConfig": "204"}, {"size": 9948, "mtime": 1748522688462, "results": "223", "hashOfConfig": "204"}, {"size": 15791, "mtime": 1748931717559, "results": "224", "hashOfConfig": "204"}, {"size": 1418, "mtime": 1748931717560, "results": "225", "hashOfConfig": "204"}, {"size": 17087, "mtime": 1748577220731, "results": "226", "hashOfConfig": "204"}, {"size": 13496, "mtime": 1748577220732, "results": "227", "hashOfConfig": "204"}, {"size": 15169, "mtime": 1748931717604, "results": "228", "hashOfConfig": "204"}, {"size": 6020, "mtime": 1748577220730, "results": "229", "hashOfConfig": "204"}, {"size": 5081, "mtime": 1748931700862, "results": "230", "hashOfConfig": "204"}, {"size": 7879, "mtime": 1748931700867, "results": "231", "hashOfConfig": "204"}, {"size": 12838, "mtime": 1748931673717, "results": "232", "hashOfConfig": "204"}, {"size": 2170, "mtime": 1748931717558, "results": "233", "hashOfConfig": "204"}, {"size": 34700, "mtime": 1748931700864, "results": "234", "hashOfConfig": "204"}, {"size": 2936, "mtime": 1748931717557, "results": "235", "hashOfConfig": "204"}, {"size": 8267, "mtime": 1748931700851, "results": "236", "hashOfConfig": "204"}, {"size": 4588, "mtime": 1748931700865, "results": "237", "hashOfConfig": "204"}, {"size": 8098, "mtime": 1748931700866, "results": "238", "hashOfConfig": "204"}, {"size": 1503, "mtime": 1748577220768, "results": "239", "hashOfConfig": "204"}, {"size": 520, "mtime": 1748406924449, "results": "240", "hashOfConfig": "204"}, {"size": 1051, "mtime": 1748406962467, "results": "241", "hashOfConfig": "204"}, {"size": 1006, "mtime": 1748942595099, "results": "242", "hashOfConfig": "204"}, {"size": 819, "mtime": 1748406934175, "results": "243", "hashOfConfig": "204"}, {"size": 804, "mtime": 1748406931614, "results": "244", "hashOfConfig": "204"}, {"size": 759, "mtime": 1748406938656, "results": "245", "hashOfConfig": "204"}, {"size": 762, "mtime": 1748406944863, "results": "246", "hashOfConfig": "204"}, {"size": 790, "mtime": 1748931700917, "results": "247", "hashOfConfig": "204"}, {"size": 1540, "mtime": 1748931717671, "results": "248", "hashOfConfig": "204"}, {"size": 822, "mtime": 1748931700911, "results": "249", "hashOfConfig": "204"}, {"size": 2502, "mtime": 1748931717672, "results": "250", "hashOfConfig": "204"}, {"size": 711, "mtime": 1748931717671, "results": "251", "hashOfConfig": "204"}, {"size": 3648, "mtime": 1748931717617, "results": "252", "hashOfConfig": "204"}, {"size": 10309, "mtime": 1748931717617, "results": "253", "hashOfConfig": "204"}, {"size": 5830, "mtime": 1748931717616, "results": "254", "hashOfConfig": "204"}, {"size": 14290, "mtime": 1748931700849, "results": "255", "hashOfConfig": "204"}, {"size": 2903, "mtime": 1749883647002, "results": "256", "hashOfConfig": "204"}, {"size": 8680, "mtime": 1747124732175, "results": "257", "hashOfConfig": "204"}, {"size": 8383, "mtime": 1748931717587, "results": "258", "hashOfConfig": "204"}, {"size": 10366, "mtime": 1748931673713, "results": "259", "hashOfConfig": "204"}, {"size": 9103, "mtime": 1748931700889, "results": "260", "hashOfConfig": "204"}, {"size": 7977, "mtime": 1748931717586, "results": "261", "hashOfConfig": "204"}, {"size": 428, "mtime": 1748931717639, "results": "262", "hashOfConfig": "204"}, {"size": 1744, "mtime": 1748931700840, "results": "263", "hashOfConfig": "204"}, {"size": 5371, "mtime": 1748931673690, "results": "264", "hashOfConfig": "204"}, {"size": 4605, "mtime": 1748931717572, "results": "265", "hashOfConfig": "204"}, {"size": 3891, "mtime": 1748407155402, "results": "266", "hashOfConfig": "204"}, {"size": 7136, "mtime": 1749881113570, "results": "267", "hashOfConfig": "204"}, {"size": 3839, "mtime": 1748407157768, "results": "268", "hashOfConfig": "204"}, {"size": 4440, "mtime": 1748577220703, "results": "269", "hashOfConfig": "204"}, {"size": 3614, "mtime": 1748931700844, "results": "270", "hashOfConfig": "204"}, {"size": 12083, "mtime": 1749883708572, "results": "271", "hashOfConfig": "204"}, {"size": 14918, "mtime": 1748931717573, "results": "272", "hashOfConfig": "204"}, {"size": 3509, "mtime": 1748931700840, "results": "273", "hashOfConfig": "204"}, {"size": 3545, "mtime": 1748931717575, "results": "274", "hashOfConfig": "204"}, {"size": 4806, "mtime": 1748931717574, "results": "275", "hashOfConfig": "204"}, {"size": 1767, "mtime": 1747124732210, "results": "276", "hashOfConfig": "204"}, {"size": 14741, "mtime": 1748931717610, "results": "277", "hashOfConfig": "204"}, {"size": 6154, "mtime": 1748931700872, "results": "278", "hashOfConfig": "204"}, {"size": 157, "mtime": 1748931717634, "results": "279", "hashOfConfig": "204"}, {"size": 2035, "mtime": 1748931717656, "results": "280", "hashOfConfig": "204"}, {"size": 982, "mtime": 1747124732082, "results": "281", "hashOfConfig": "204"}, {"size": 421, "mtime": 1747124732095, "results": "282", "hashOfConfig": "204"}, {"size": 31332, "mtime": 1749620217948, "results": "283", "hashOfConfig": "204"}, {"size": 810, "mtime": 1748577220695, "results": "284", "hashOfConfig": "204"}, {"size": 691, "mtime": 1747124732082, "results": "285", "hashOfConfig": "204"}, {"size": 1147, "mtime": 1748931700888, "results": "286", "hashOfConfig": "204"}, {"size": 11441, "mtime": 1748931717676, "results": "287", "hashOfConfig": "204"}, {"size": 1998, "mtime": 1748931717676, "results": "288", "hashOfConfig": "204"}, {"size": 9000, "mtime": 1748931717675, "results": "289", "hashOfConfig": "204"}, {"size": 852, "mtime": 1748412455335, "results": "290", "hashOfConfig": "204"}, {"size": 538, "mtime": 1747124732095, "results": "291", "hashOfConfig": "204"}, {"size": 887, "mtime": 1747124732095, "results": "292", "hashOfConfig": "204"}, {"size": 258, "mtime": 1748407111255, "results": "293", "hashOfConfig": "204"}, {"size": 418, "mtime": 1748931700894, "results": "294", "hashOfConfig": "204"}, {"size": 559, "mtime": 1747124732082, "results": "295", "hashOfConfig": "204"}, {"size": 4324, "mtime": 1748522688026, "results": "296", "hashOfConfig": "204"}, {"size": 8047, "mtime": 1748522688023, "results": "297", "hashOfConfig": "204"}, {"size": 1478, "mtime": 1747124732268, "results": "298", "hashOfConfig": "204"}, {"size": 13604, "mtime": 1748931717588, "results": "299", "hashOfConfig": "204"}, {"size": 1176, "mtime": 1748931717639, "results": "300", "hashOfConfig": "204"}, {"size": 6388, "mtime": 1748931673725, "results": "301", "hashOfConfig": "204"}, {"size": 7340, "mtime": 1748931673725, "results": "302", "hashOfConfig": "204"}, {"size": 5652, "mtime": 1748931673714, "results": "303", "hashOfConfig": "204"}, {"size": 765, "mtime": 1748931700892, "results": "304", "hashOfConfig": "204"}, {"size": 2107, "mtime": 1747124732082, "results": "305", "hashOfConfig": "204"}, {"size": 442, "mtime": 1747124732082, "results": "306", "hashOfConfig": "204"}, {"size": 2293, "mtime": 1747124732157, "results": "307", "hashOfConfig": "204"}, {"size": 4375, "mtime": 1749628571517, "results": "308", "hashOfConfig": "204"}, {"size": 1066, "mtime": 1747124732082, "results": "309", "hashOfConfig": "204"}, {"size": 1389, "mtime": 1748931717675, "results": "310", "hashOfConfig": "204"}, {"size": 103, "mtime": 1747124732095, "results": "311", "hashOfConfig": "204"}, {"size": 5107, "mtime": 1749628700868, "results": "312", "hashOfConfig": "204"}, {"size": 11215, "mtime": 1748931673712, "results": "313", "hashOfConfig": "204"}, {"size": 5279, "mtime": 1747124732263, "results": "314", "hashOfConfig": "204"}, {"size": 6649, "mtime": 1748931700869, "results": "315", "hashOfConfig": "204"}, {"size": 4086, "mtime": 1748931673727, "results": "316", "hashOfConfig": "204"}, {"size": 8457, "mtime": 1748931717607, "results": "317", "hashOfConfig": "204"}, {"size": 2827, "mtime": 1748931673728, "results": "318", "hashOfConfig": "204"}, {"size": 7765, "mtime": 1748931700871, "results": "319", "hashOfConfig": "204"}, {"size": 4366, "mtime": 1748931673729, "results": "320", "hashOfConfig": "204"}, {"size": 276, "mtime": 1748931673728, "results": "321", "hashOfConfig": "204"}, {"size": 8077, "mtime": 1748931717614, "results": "322", "hashOfConfig": "204"}, {"size": 8730, "mtime": 1748931717611, "results": "323", "hashOfConfig": "204"}, {"size": 935, "mtime": 1748931717638, "results": "324", "hashOfConfig": "204"}, {"size": 1299, "mtime": 1748407122979, "results": "325", "hashOfConfig": "204"}, {"size": 1420, "mtime": 1748407093178, "results": "326", "hashOfConfig": "204"}, {"size": 223, "mtime": 1748407072019, "results": "327", "hashOfConfig": "204"}, {"size": 1388, "mtime": 1748407090945, "results": "328", "hashOfConfig": "204"}, {"size": 1320, "mtime": 1748407095953, "results": "329", "hashOfConfig": "204"}, {"size": 570, "mtime": 1748407067480, "results": "330", "hashOfConfig": "204"}, {"size": 377, "mtime": 1748407074575, "results": "331", "hashOfConfig": "204"}, {"size": 720, "mtime": 1748407105341, "results": "332", "hashOfConfig": "204"}, {"size": 39922, "mtime": 1747124732095, "results": "333", "hashOfConfig": "204"}, {"size": 19835, "mtime": 1749883708580, "results": "334", "hashOfConfig": "204"}, {"size": 1214, "mtime": 1748931717652, "results": "335", "hashOfConfig": "204"}, {"size": 6427, "mtime": 1749883708585, "results": "336", "hashOfConfig": "204"}, {"size": 1904, "mtime": 1749883708585, "results": "337", "hashOfConfig": "204"}, {"size": 6225, "mtime": 1749883708581, "results": "338", "hashOfConfig": "204"}, {"size": 11619, "mtime": 1749883708584, "results": "339", "hashOfConfig": "204"}, {"size": 9695, "mtime": 1749883647020, "results": "340", "hashOfConfig": "204"}, {"size": 7851, "mtime": 1748931700873, "results": "341", "hashOfConfig": "204"}, {"size": 2762, "mtime": 1748522688462, "results": "342", "hashOfConfig": "204"}, {"size": 7115, "mtime": 1747124732210, "results": "343", "hashOfConfig": "204"}, {"size": 6657, "mtime": 1748522688462, "results": "344", "hashOfConfig": "204"}, {"size": 425, "mtime": 1749892531655, "results": "345", "hashOfConfig": "204"}, {"size": 1292, "mtime": 1749883708587, "results": "346", "hashOfConfig": "204"}, {"size": 19730, "mtime": 1749897894520, "results": "347", "hashOfConfig": "204"}, {"size": 3443, "mtime": 1747124732268, "results": "348", "hashOfConfig": "204"}, {"size": 4977, "mtime": 1747124732263, "results": "349", "hashOfConfig": "204"}, {"size": 6426, "mtime": 1748522688413, "results": "350", "hashOfConfig": "204"}, {"size": 207, "mtime": 1748931717608, "results": "351", "hashOfConfig": "204"}, {"size": 853, "mtime": 1747124732167, "results": "352", "hashOfConfig": "204"}, {"size": 25088, "mtime": 1749883708579, "results": "353", "hashOfConfig": "204"}, {"size": 13333, "mtime": 1748577220725, "results": "354", "hashOfConfig": "204"}, {"size": 13294, "mtime": 1748931717619, "results": "355", "hashOfConfig": "204"}, {"size": 4868, "mtime": 1748931717620, "results": "356", "hashOfConfig": "204"}, {"size": 6593, "mtime": 1749897972133, "results": "357", "hashOfConfig": "204"}, {"size": 2025, "mtime": 1748931717642, "results": "358", "hashOfConfig": "204"}, {"size": 739, "mtime": 1748931717644, "results": "359", "hashOfConfig": "204"}, {"size": 3197, "mtime": 1749883708586, "results": "360", "hashOfConfig": "204"}, {"size": 1620, "mtime": 1749883708581, "results": "361", "hashOfConfig": "204"}, {"size": 693, "mtime": 1749883708582, "results": "362", "hashOfConfig": "204"}, {"size": 634, "mtime": 1749883708583, "results": "363", "hashOfConfig": "204"}, {"size": 675, "mtime": 1749883708583, "results": "364", "hashOfConfig": "204"}, {"size": 185, "mtime": 1747124732095, "results": "365", "hashOfConfig": "204"}, {"size": 2375, "mtime": 1749883708582, "results": "366", "hashOfConfig": "204"}, {"size": 19078, "mtime": 1749883647036, "results": "367", "hashOfConfig": "204"}, {"size": 15339, "mtime": 1748931717623, "results": "368", "hashOfConfig": "204"}, {"size": 20031, "mtime": 1748931717622, "results": "369", "hashOfConfig": "204"}, {"size": 5352, "mtime": 1749628571486, "results": "370", "hashOfConfig": "204"}, {"size": 2880, "mtime": 1749628571441, "results": "371", "hashOfConfig": "204"}, {"size": 4065, "mtime": 1749628571458, "results": "372", "hashOfConfig": "204"}, {"size": 3060, "mtime": 1748931717582, "results": "373", "hashOfConfig": "204"}, {"size": 27946, "mtime": 1749883646989, "results": "374", "hashOfConfig": "204"}, {"size": 6582, "mtime": 1748931717585, "results": "375", "hashOfConfig": "204"}, {"size": 5723, "mtime": 1749628571471, "results": "376", "hashOfConfig": "204"}, {"size": 8635, "mtime": 1748931673707, "results": "377", "hashOfConfig": "204"}, {"size": 1364, "mtime": 1749883646955, "results": "378", "hashOfConfig": "204"}, {"size": 1849, "mtime": 1749883708586, "results": "379", "hashOfConfig": "204"}, {"size": 272, "mtime": 1749714907747, "results": "380", "hashOfConfig": "204"}, {"size": 532, "mtime": 1749883646956, "results": "381", "hashOfConfig": "204"}, {"size": 4253, "mtime": 1749890030051, "results": "382", "hashOfConfig": "204"}, {"size": 131, "mtime": 1749883708577, "results": "383", "hashOfConfig": "204"}, {"size": 5121, "mtime": 1749883708577, "results": "384", "hashOfConfig": "204"}, {"size": 5407, "mtime": 1749883708572, "results": "385", "hashOfConfig": "204"}, {"size": 4120, "mtime": 1749883646964, "results": "386", "hashOfConfig": "204"}, {"size": 1576, "mtime": 1749883646964, "results": "387", "hashOfConfig": "204"}, {"size": 12041, "mtime": 1749883708573, "results": "388", "hashOfConfig": "204"}, {"size": 8608, "mtime": 1749883708574, "results": "389", "hashOfConfig": "204"}, {"size": 6003, "mtime": 1749883708574, "results": "390", "hashOfConfig": "204"}, {"size": 5938, "mtime": 1749883708576, "results": "391", "hashOfConfig": "204"}, {"size": 6755, "mtime": 1749883708575, "results": "392", "hashOfConfig": "204"}, {"size": 5805, "mtime": 1749883708576, "results": "393", "hashOfConfig": "204"}, {"size": 6003, "mtime": 1749883708574, "results": "394", "hashOfConfig": "204"}, {"size": 12041, "mtime": 1749883708573, "results": "395", "hashOfConfig": "204"}, {"size": 10327, "mtime": 1750144742772, "results": "396", "hashOfConfig": "204"}, {"size": 7384, "mtime": 1750142871203, "results": "397", "hashOfConfig": "204"}, {"size": 7997, "mtime": 1750144301220, "results": "398", "hashOfConfig": "204"}, {"size": 8608, "mtime": 1749889937971, "results": "399", "hashOfConfig": "204"}, {"size": 1174, "mtime": 1750149219809, "results": "400", "hashOfConfig": "204"}, {"size": 3067, "mtime": 1749892550396, "results": "401", "hashOfConfig": "204"}, {"size": 9517, "mtime": 1750145276438, "results": "402", "hashOfConfig": "204"}, {"size": 13131, "mtime": 1750150470101, "results": "403", "hashOfConfig": "204"}, {"size": 15318, "mtime": 1750150391278, "results": "404", "hashOfConfig": "204"}, {"size": 7832, "mtime": 1750150527664, "results": "405", "hashOfConfig": "204"}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "utrof9", {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\reportWebVitals.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\store.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\routes.js", ["1012"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\theme.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\reducers.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Backgroundprovider.jsx", ["1013", "1014"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Create.js", ["1015"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\MainLayout.js", ["1016", "1017"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\AuthLayout.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Auth\\Login.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PermissionRoute.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveApproval\\Approval.jsx", ["1018", "1019"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\config\\permissionConfig.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\permission.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveConfiguration\\LeaveConfiguration.jsx", ["1020", "1021"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveReport\\LeaveReport.jsx", ["1022"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Tasklist.jsx", ["1023"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveCalendar\\LeaveCalendar.jsx", ["1024"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductList.jsx", ["1025"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TaskHistoryAdmin.jsx", ["1026"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductTimesheet.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\NotFound.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductOverview.jsx", ["1027"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\AccessDenied.jsx", ["1028"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Client\\Client.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\ProductListStaff.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\TaskListWithNote.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\GeneralSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AuthSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\UserSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AttendanceSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DesignationSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DepartmentSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ExpensesSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\LeaveSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\TimelineSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ProductSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ClientSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SprintSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SettingSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\UserSprintPage.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintTasksPage.jsx", ["1029", "1030"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintPage.jsx", ["1031", "1032"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\index.js", ["1033"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AuthSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\UserSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DepartmentSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DesignationSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AttendanceSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ExpenseSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\LeaveSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\TimelineSaga.js", ["1034"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ActivitySaga.js", ["1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ProductSaga.js", ["1045", "1046"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ClientSaga.js", ["1047", "1048", "1049"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SprintSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SettingSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Setting\\index.js", ["1050"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\index.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\actions.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PageTitle.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\role.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Permission.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Input.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\SelectField.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\constants\\menus.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\menuGenerator.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\permissionLogger.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\can.js", ["1051"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\FormSkeleton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\sort.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\leaveConst.js", ["1052"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SettingSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\TimelineSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\FloatingButton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\MenuForm.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductAdd.jsx", ["1053"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SprintSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TimesheetFilters.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\DateRangeSelector.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ProductSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\CustomMenu.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\ListSkeleton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Charts.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widgets.js", ["1054", "1055", "1056", "1057", "1058"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\DialogConfirm.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\convertion.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\expenseStatus.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\UserLeaveInfo.jsx", ["1059"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\LeaveCalender.jsx", ["1060", "1061"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Filter.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskFilterUser.jsx", ["1062"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\AddMembers.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskHeader.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeader.jsx", ["1063", "1064"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskInfoComponent.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeaderFilterStaff.jsx", ["1065"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\Note.jsx", ["1066"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintList.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintForm.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\GeneralSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\UserSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DesignationSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AuthSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DepartmentSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ExpensesSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AttendanceSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ClientSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\LeaveSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\countries.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\TimelineNew.jsx", ["1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SettingService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SprintService.js", ["1083", "1084"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\TimelineService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ActivityService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ProductService.js", ["1085"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\EmployeeList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\AttendanceList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\ExpensesList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\apiConfig.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Leave.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Attendance.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\constant\\ProductConts.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widget.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Activity.js", ["1086", "1087"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\FormLeavePop.jsx", ["1088", "1089", "1090"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\api.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AuthService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DepartmentService.js", ["1091"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\UserService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AttendanceService.js", ["1092"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DesignationService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ExpensesService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\LeaveService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\gender.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ClientService.js", ["1093"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\WeekView.jsx", ["1094", "1095", "1096", "1097"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\MonthView.jsx", ["1098", "1099", "1100", "1101"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\DayView.jsx", ["1102", "1103", "1104"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TodayGoal.jsx", ["1105", "1106", "1107", "1108"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\BreakReasone.jsx", ["1109", "1110"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\EarlyLate.jsx", ["1111", "1112"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\AttendanceBarChart.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\ProductivityChart.jsx", ["1113", "1114", "1115", "1116", "1117"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\WorkHoursStatus.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\OverLimitBreak.jsx", ["1118"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TimelineRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\App.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ActivitySlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ActivitySelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\LoadingScreen.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\WorkSchedule.jsx", ["1119", "1120", "1121"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TaskRequest\\TaskRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TimeRequest\\TimeRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\Overview.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\AdminDashboard.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\UserDashboard.js", ["1122"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\MonthlyWorkReport.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\DayWorkReport.jsx", ["1123"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\WeekWorkReport.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\WeekWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\workSchedule.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\WorkScheduleService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkScheduleForm.jsx", ["1124"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkScheduleForm.jsx", ["1125"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\AdvancedWorkScheduleForm.jsx", ["1126"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\workScheduleUtils.js", [], [], {"ruleId": "1127", "severity": 1, "message": "1128", "line": 390, "column": 12, "nodeType": "1129", "messageId": "1130", "endLine": 390, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1131", "line": 1, "column": 21, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 28}, {"ruleId": "1127", "severity": 1, "message": "1132", "line": 18, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1133", "line": 21, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 21, "endColumn": 18}, {"ruleId": "1127", "severity": 1, "message": "1134", "line": 31, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 31, "endColumn": 11}, {"ruleId": "1127", "severity": 1, "message": "1135", "line": 36, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 36, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1136", "line": 14, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1137", "line": 40, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 40, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1136", "line": 14, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 7}, {"ruleId": "1127", "severity": 1, "message": "1138", "line": 16, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 16, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1139", "line": 66, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 66, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1140", "line": 15, "column": 7, "nodeType": "1129", "messageId": "1130", "endLine": 15, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1131", "line": 9, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 9, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1140", "line": 27, "column": 7, "nodeType": "1129", "messageId": "1130", "endLine": 27, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1141", "line": 46, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 46, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1142", "line": 204, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 204, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1143", "line": 5, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 5, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1144", "line": 22, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 22, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1145", "line": 36, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 36, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1146", "line": 34, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 34, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1147", "line": 131, "column": 7, "nodeType": "1129", "messageId": "1130", "endLine": 131, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1148", "line": 50, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 50, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1149", "line": 11, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 11, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1149", "line": 11, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 11, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 95, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 95, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 117, "column": 13, "nodeType": "1129", "messageId": "1130", "endLine": 117, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 143, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 143, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 165, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 165, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 186, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 186, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1149", "line": 206, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 206, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 227, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 227, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 248, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 248, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 269, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 269, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1149", "line": 45, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 45, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1150", "line": 92, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 92, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1149", "line": 12, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 12, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1149", "line": 33, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 33, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1149", "line": 55, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 55, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1151", "line": 8, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 8, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1152", "line": 7, "column": 7, "nodeType": "1129", "messageId": "1130", "endLine": 7, "endColumn": 10}, {"ruleId": "1127", "severity": 1, "message": "1153", "line": 1, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 16}, {"ruleId": "1127", "severity": 1, "message": "1131", "line": 2, "column": 21, "nodeType": "1129", "messageId": "1130", "endLine": 2, "endColumn": 28}, {"ruleId": "1127", "severity": 1, "message": "1154", "line": 4, "column": 31, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 52}, {"ruleId": "1127", "severity": 1, "message": "1155", "line": 8, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 8, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1156", "line": 23, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 23, "endColumn": 18}, {"ruleId": "1127", "severity": 1, "message": "1157", "line": 24, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 24, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1158", "line": 25, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 25, "endColumn": 18}, {"ruleId": "1127", "severity": 1, "message": "1159", "line": 16, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 16, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1160", "line": 1, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1161", "line": 3, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1146", "line": 25, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 25, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1162", "line": 2, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 2, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1131", "line": 12, "column": 21, "nodeType": "1129", "messageId": "1130", "endLine": 12, "endColumn": 28}, {"ruleId": "1127", "severity": 1, "message": "1163", "line": 1, "column": 42, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 51}, {"ruleId": "1127", "severity": 1, "message": "1136", "line": 1, "column": 16, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1164", "line": 3, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 8}, {"ruleId": "1127", "severity": 1, "message": "1163", "line": 3, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1165", "line": 3, "column": 21, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 30}, {"ruleId": "1127", "severity": 1, "message": "1166", "line": 3, "column": 32, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 46}, {"ruleId": "1127", "severity": 1, "message": "1167", "line": 3, "column": 48, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 57}, {"ruleId": "1127", "severity": 1, "message": "1168", "line": 3, "column": 59, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 67}, {"ruleId": "1127", "severity": 1, "message": "1169", "line": 3, "column": 69, "nodeType": "1129", "messageId": "1130", "endLine": 3, "endColumn": 74}, {"ruleId": "1127", "severity": 1, "message": "1170", "line": 4, "column": 16, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 27}, {"ruleId": "1127", "severity": 1, "message": "1171", "line": 4, "column": 39, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 45}, {"ruleId": "1127", "severity": 1, "message": "1172", "line": 4, "column": 47, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 58}, {"ruleId": "1127", "severity": 1, "message": "1173", "line": 4, "column": 60, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 73}, {"ruleId": "1127", "severity": 1, "message": "1174", "line": 4, "column": 75, "nodeType": "1129", "messageId": "1130", "endLine": 4, "endColumn": 88}, {"ruleId": "1127", "severity": 1, "message": "1175", "line": 5, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 5, "endColumn": 13}, {"ruleId": "1127", "severity": 1, "message": "1176", "line": 5, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 5, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1177", "line": 5, "column": 23, "nodeType": "1129", "messageId": "1130", "endLine": 5, "endColumn": 30}, {"ruleId": "1127", "severity": 1, "message": "1178", "line": 30, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 30, "endColumn": 14}, {"ruleId": "1127", "severity": 1, "message": "1179", "line": 1, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1180", "line": 14, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 26}, {"ruleId": "1127", "severity": 1, "message": "1179", "line": 1, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1181", "line": 17, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1132", "line": 67, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 67, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1182", "line": 6, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 6, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1183", "line": 17, "column": 39, "nodeType": "1129", "messageId": "1130", "endLine": 17, "endColumn": 50}, {"ruleId": "1127", "severity": 1, "message": "1184", "line": 21, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 21, "endColumn": 20}, {"ruleId": "1127", "severity": 1, "message": "1185", "line": 2, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 2, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1186", "line": 1, "column": 14, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 18}, {"ruleId": "1127", "severity": 1, "message": "1179", "line": 1, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1166", "line": 8, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 8, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1169", "line": 11, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 11, "endColumn": 8}, {"ruleId": "1127", "severity": 1, "message": "1187", "line": 67, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 67, "endColumn": 15}, {"ruleId": "1127", "severity": 1, "message": "1188", "line": 113, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 113, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1189", "line": 8, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 8, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1190", "line": 75, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 75, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1188", "line": 283, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 283, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1191", "line": 422, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 422, "endColumn": 22}, {"ruleId": "1127", "severity": 1, "message": "1166", "line": 9, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 9, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1169", "line": 12, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 12, "endColumn": 8}, {"ruleId": "1127", "severity": 1, "message": "1188", "line": 98, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 98, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1192", "line": 11, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 11, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1193", "line": 24, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 24, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1194", "line": 25, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 25, "endColumn": 24}, {"ruleId": "1127", "severity": 1, "message": "1195", "line": 28, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 28, "endColumn": 17}, {"ruleId": "1127", "severity": 1, "message": "1196", "line": 1, "column": 36, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 43}, {"ruleId": "1127", "severity": 1, "message": "1197", "line": 5, "column": 22, "nodeType": "1129", "messageId": "1130", "endLine": 5, "endColumn": 33}, {"ruleId": "1127", "severity": 1, "message": "1197", "line": 10, "column": 23, "nodeType": "1129", "messageId": "1130", "endLine": 10, "endColumn": 34}, {"ruleId": "1127", "severity": 1, "message": "1198", "line": 34, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 34, "endColumn": 19}, {"ruleId": "1127", "severity": 1, "message": "1160", "line": 14, "column": 38, "nodeType": "1129", "messageId": "1130", "endLine": 14, "endColumn": 48}, {"ruleId": "1127", "severity": 1, "message": "1161", "line": 18, "column": 10, "nodeType": "1129", "messageId": "1130", "endLine": 18, "endColumn": 21}, {"ruleId": "1127", "severity": 1, "message": "1199", "line": 412, "column": 25, "nodeType": "1129", "messageId": "1130", "endLine": 412, "endColumn": 32}, {"ruleId": "1127", "severity": 1, "message": "1200", "line": 417, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 417, "endColumn": 33}, {"ruleId": "1127", "severity": 1, "message": "1201", "line": 419, "column": 15, "nodeType": "1129", "messageId": "1130", "endLine": 419, "endColumn": 31}, {"ruleId": "1127", "severity": 1, "message": "1193", "line": 23, "column": 12, "nodeType": "1129", "messageId": "1130", "endLine": 23, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1202", "line": 1, "column": 27, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 36}, {"ruleId": "1127", "severity": 1, "message": "1203", "line": 33, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 33, "endColumn": 28}, {"ruleId": "1127", "severity": 1, "message": "1204", "line": 56, "column": 9, "nodeType": "1129", "messageId": "1130", "endLine": 56, "endColumn": 23}, {"ruleId": "1127", "severity": 1, "message": "1156", "line": 13, "column": 11, "nodeType": "1129", "messageId": "1130", "endLine": 13, "endColumn": 18}, {"ruleId": "1127", "severity": 1, "message": "1205", "line": 23, "column": 8, "nodeType": "1129", "messageId": "1130", "endLine": 23, "endColumn": 18}, {"ruleId": "1127", "severity": 1, "message": "1206", "line": 1, "column": 17, "nodeType": "1129", "messageId": "1130", "endLine": 1, "endColumn": 25}, {"ruleId": "1127", "severity": 1, "message": "1207", "line": 21, "column": 30, "nodeType": "1129", "messageId": "1130", "endLine": 21, "endColumn": 44}, {"ruleId": "1127", "severity": 1, "message": "1208", "line": 12, "column": 3, "nodeType": "1129", "messageId": "1130", "endLine": 12, "endColumn": 7}, "no-unused-vars", "'userRole' is assigned a value but never used.", "Identifier", "unusedVar", "'element' is defined but never used.", "'slotController' is assigned a value but never used.", "'Permission' is defined but never used.", "'Can' is defined but never used.", "'ROLES' is defined but never used.", "'Grid' is defined but never used.", "'users' is assigned a value but never used.", "'DialogContentText' is defined but never used.", "'daysOfMonth' is assigned a value but never used.", "'FilterBox' is assigned a value but never used.", "'history' is assigned a value but never used.", "'totalSpent' is assigned a value but never used.", "'ErrorOutlineIcon' is defined but never used.", "'Divider' is defined but never used.", "'currentUser' is assigned a value but never used.", "'products' is assigned a value but never used.", "'isMySprintPage' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'result' is assigned a value but never used.", "'data' is assigned a value but never used.", "'SettingSelector' is defined but never used.", "'log' is assigned a value but never used.", "'values' is defined but never used.", "'AssignmentIndOutlined' is defined but never used.", "'LeaveActions' is defined but never used.", "'profile' is assigned a value but never used.", "'countLeave' is assigned a value but never used.", "'setting' is assigned a value but never used.", "'upcomingLeaves' is assigned a value but never used.", "'useContext' is defined but never used.", "'backContext' is defined but never used.", "'PlayCircle' is defined but never used.", "'TableBody' is defined but never used.", "'Table' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "'ButtonGroup' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'IconButton' is defined but never used.", "'Popper' is defined but never used.", "'Popover' is defined but never used.", "'theme' is assigned a value but never used.", "'API_BASE_URL' is defined but never used.", "'queryParams' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "'InputBase' is defined but never used.", "'UserActions' is defined but never used.", "'LeaveStatus' is defined but never used.", "'getApiUrl' is defined but never used.", "'post' is defined but never used.", "'totals' is assigned a value but never used.", "'formatTimeForTooltip' is assigned a value but never used.", "'monthData' is assigned a value but never used.", "'getMonthName' is assigned a value but never used.", "'monthlyTotals' is assigned a value but never used.", "'ActivityActions' is defined but never used.", "'todayActivity' is assigned a value but never used.", "'todayHistoryArr' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'useMemo' is defined but never used.", "'useSelector' is defined but never used.", "'body' is assigned a value but never used.", "'endHour' is assigned a value but never used.", "'startTimeFormatted' is assigned a value but never used.", "'endTimeFormatted' is assigned a value but never used.", "'useEffect' is defined but never used.", "'getCurrentDateRange' is assigned a value but never used.", "'lastFetchedRef' is assigned a value but never used.", "'formatTime' is assigned a value but never used.", "'useState' is defined but never used.", "'SCHEDULE_TYPES' is defined but never used.", "'Chip' is defined but never used."]