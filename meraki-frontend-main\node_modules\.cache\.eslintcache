[{"E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\index.js": "1", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\reportWebVitals.js": "2", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\store.js": "3", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\routes.js": "4", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\theme.js": "5", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\reducers.js": "6", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\index.js": "7", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Backgroundprovider.jsx": "8", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Create.js": "9", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Form.js": "10", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\MainLayout.js": "11", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\AuthLayout.js": "12", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\Form.js": "13", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\Form.js": "14", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Auth\\Login.js": "15", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\Form.js": "16", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\Form.js": "17", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PermissionRoute.js": "18", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveApproval\\Approval.jsx": "19", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\Form.js": "20", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\config\\permissionConfig.js": "21", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\permission.js": "22", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveConfiguration\\LeaveConfiguration.jsx": "23", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveReport\\LeaveReport.jsx": "24", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Tasklist.jsx": "25", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveCalendar\\LeaveCalendar.jsx": "26", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductList.jsx": "27", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TaskHistoryAdmin.jsx": "28", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductTimesheet.jsx": "29", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\NotFound.jsx": "30", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductOverview.jsx": "31", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\AccessDenied.jsx": "32", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Client\\Client.jsx": "33", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\ProductListStaff.jsx": "34", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\TaskListWithNote.jsx": "35", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\GeneralSlice.js": "36", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AuthSlice.js": "37", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\UserSlice.js": "38", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AttendanceSlice.js": "39", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DesignationSlice.js": "40", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DepartmentSlice.js": "41", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ExpensesSlice.js": "42", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\LeaveSlice.js": "43", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\TimelineSlice.js": "44", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ProductSlice.js": "45", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ClientSlice.js": "46", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SprintSlice.js": "47", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SettingSlice.js": "48", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\UserSprintPage.jsx": "49", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintTasksPage.jsx": "50", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintPage.jsx": "51", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\index.js": "52", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\index.js": "53", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\index.js": "54", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\index.js": "55", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\index.js": "56", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\index.js": "57", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\index.js": "58", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\index.js": "59", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AuthSaga.js": "60", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\UserSaga.js": "61", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DepartmentSaga.js": "62", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DesignationSaga.js": "63", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AttendanceSaga.js": "64", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ExpenseSaga.js": "65", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\LeaveSaga.js": "66", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\TimelineSaga.js": "67", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ActivitySaga.js": "68", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ProductSaga.js": "69", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ClientSaga.js": "70", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SprintSaga.js": "71", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SettingSaga.js": "72", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\index.js": "73", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Setting\\index.js": "74", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\index.js": "75", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\index.jsx": "76", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\actions.js": "77", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PageTitle.js": "78", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\role.js": "79", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Permission.jsx": "80", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Input.js": "81", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\SelectField.js": "82", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\constants\\menus.js": "83", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\menuGenerator.js": "84", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\permissionLogger.js": "85", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\can.js": "86", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\FormSkeleton.js": "87", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\sort.js": "88", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\leaveConst.js": "89", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SettingSelector.js": "90", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\TimelineSelector.js": "91", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\FloatingButton.js": "92", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\AccountSetting.js": "93", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\BasicInformation.js": "94", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\MenuForm.js": "95", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductAdd.jsx": "96", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SprintSelector.js": "97", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TimesheetFilters.jsx": "98", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\WeeklyPicker.jsx": "99", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\DateRangeSelector.jsx": "100", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ProductSelector.js": "101", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\CustomMenu.js": "102", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\ListSkeleton.js": "103", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Charts.js": "104", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widgets.js": "105", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\DialogConfirm.js": "106", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\convertion.js": "107", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\expenseStatus.js": "108", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\UserLeaveInfo.jsx": "109", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\LeaveCalender.jsx": "110", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Filter.js": "111", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskFilterUser.jsx": "112", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\AddMembers.jsx": "113", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskHeader.jsx": "114", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeader.jsx": "115", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskInfoComponent.jsx": "116", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeaderFilterStaff.jsx": "117", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\Note.jsx": "118", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintList.jsx": "119", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintForm.jsx": "120", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\GeneralSelector.js": "121", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\UserSelector.js": "122", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DesignationSelector.js": "123", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AuthSelector.js": "124", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DepartmentSelector.js": "125", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ExpensesSelector.js": "126", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AttendanceSelector.js": "127", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ClientSelector.js": "128", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\LeaveSelector.js": "129", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\countries.js": "130", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\TimelineNew.jsx": "131", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SettingService.js": "132", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SprintService.js": "133", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\TimelineService.js": "134", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ActivityService.js": "135", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ProductService.js": "136", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\EmployeeList.js": "137", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\AttendanceList.js": "138", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\AccountSetting.js": "139", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\ExpensesList.js": "140", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\BasicInformation.js": "141", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\index.js": "142", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\apiConfig.js": "143", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\BasicInformation.js": "144", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Leave.js": "145", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Attendance.js": "146", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\AccountSetting.js": "147", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\constant\\ProductConts.js": "148", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widget.js": "149", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Activity.js": "150", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\FormLeavePop.jsx": "151", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\DayPicker.jsx": "152", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\MonthPicker.jsx": "153", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\api.js": "154", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AuthService.js": "155", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DepartmentService.js": "156", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\UserService.js": "157", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AttendanceService.js": "158", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DesignationService.js": "159", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ExpensesService.js": "160", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\LeaveService.js": "161", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\gender.js": "162", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ClientService.js": "163", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\WeekView.jsx": "164", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\MonthView.jsx": "165", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\DayView.jsx": "166", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TodayGoal.jsx": "167", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\BreakReasone.jsx": "168", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\EarlyLate.jsx": "169", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\AttendanceBarChart.jsx": "170", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\ProductivityChart.jsx": "171", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\WorkHoursStatus.jsx": "172", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\OverLimitBreak.jsx": "173", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TimelineRequest.jsx": "174", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\App.js": "175", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ActivitySlice.js": "176", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ActivitySelector.js": "177", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\LoadingScreen.js": "178", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\WorkSchedule.jsx": "179", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TaskRequest\\TaskRequest.jsx": "180", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TimeRequest\\TimeRequest.jsx": "181", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\Overview.jsx": "182", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\AdminDashboard.js": "183", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\UserDashboard.js": "184", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\DayPicker.jsx": "185", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\WeeklyPicker.jsx": "186", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\MonthPicker.jsx": "187", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\MonthlyWorkReport.jsx": "188", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\DayWorkReport.jsx": "189", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\WeekWorkReport.jsx": "190", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\MonthPicker.jsx": "191", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\DayPicker.jsx": "192", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkSchedule.jsx": "193", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\WeekWorkSchedule.jsx": "194", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkSchedule.jsx": "195", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\WeeklyPicker.jsx": "196", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\workSchedule.js": "197", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\WorkScheduleService.js": "198", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkScheduleForm.jsx": "199", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkScheduleForm.jsx": "200", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\AdvancedWorkScheduleForm.jsx": "201", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\workScheduleUtils.js": "202", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\ScheduleManager.jsx": "203"}, {"size": 850, "mtime": 1749628571403, "results": "204", "hashOfConfig": "205"}, {"size": 375, "mtime": 1747124732125, "results": "206", "hashOfConfig": "205"}, {"size": 1749, "mtime": 1748931717673, "results": "207", "hashOfConfig": "205"}, {"size": 16086, "mtime": 1749883708571, "results": "208", "hashOfConfig": "205"}, {"size": 3742, "mtime": 1747124732306, "results": "209", "hashOfConfig": "205"}, {"size": 2423, "mtime": 1748931717657, "results": "210", "hashOfConfig": "205"}, {"size": 1813, "mtime": 1748931717579, "results": "211", "hashOfConfig": "205"}, {"size": 8531, "mtime": 1748931673699, "results": "212", "hashOfConfig": "205"}, {"size": 5702, "mtime": 1749628571544, "results": "213", "hashOfConfig": "205"}, {"size": 4922, "mtime": 1747124732253, "results": "214", "hashOfConfig": "205"}, {"size": 11847, "mtime": 1748931717566, "results": "215", "hashOfConfig": "205"}, {"size": 1496, "mtime": 1748407262947, "results": "216", "hashOfConfig": "205"}, {"size": 4099, "mtime": 1748522688462, "results": "217", "hashOfConfig": "205"}, {"size": 4448, "mtime": 1748522688462, "results": "218", "hashOfConfig": "205"}, {"size": 6835, "mtime": 1748931700850, "results": "219", "hashOfConfig": "205"}, {"size": 5567, "mtime": 1747124732157, "results": "220", "hashOfConfig": "205"}, {"size": 8416, "mtime": 1748522688462, "results": "221", "hashOfConfig": "205"}, {"size": 3309, "mtime": 1748931717558, "results": "222", "hashOfConfig": "205"}, {"size": 5376, "mtime": 1748577220729, "results": "223", "hashOfConfig": "205"}, {"size": 9948, "mtime": 1748522688462, "results": "224", "hashOfConfig": "205"}, {"size": 15791, "mtime": 1748931717559, "results": "225", "hashOfConfig": "205"}, {"size": 1418, "mtime": 1748931717560, "results": "226", "hashOfConfig": "205"}, {"size": 17087, "mtime": 1748577220731, "results": "227", "hashOfConfig": "205"}, {"size": 13496, "mtime": 1748577220732, "results": "228", "hashOfConfig": "205"}, {"size": 15169, "mtime": 1748931717604, "results": "229", "hashOfConfig": "205"}, {"size": 6020, "mtime": 1748577220730, "results": "230", "hashOfConfig": "205"}, {"size": 5081, "mtime": 1748931700862, "results": "231", "hashOfConfig": "205"}, {"size": 7879, "mtime": 1748931700867, "results": "232", "hashOfConfig": "205"}, {"size": 12838, "mtime": 1748931673717, "results": "233", "hashOfConfig": "205"}, {"size": 2170, "mtime": 1748931717558, "results": "234", "hashOfConfig": "205"}, {"size": 34700, "mtime": 1748931700864, "results": "235", "hashOfConfig": "205"}, {"size": 2936, "mtime": 1748931717557, "results": "236", "hashOfConfig": "205"}, {"size": 8267, "mtime": 1748931700851, "results": "237", "hashOfConfig": "205"}, {"size": 4588, "mtime": 1748931700865, "results": "238", "hashOfConfig": "205"}, {"size": 8098, "mtime": 1748931700866, "results": "239", "hashOfConfig": "205"}, {"size": 1503, "mtime": 1748577220768, "results": "240", "hashOfConfig": "205"}, {"size": 520, "mtime": 1748406924449, "results": "241", "hashOfConfig": "205"}, {"size": 1051, "mtime": 1748406962467, "results": "242", "hashOfConfig": "205"}, {"size": 1006, "mtime": 1748942595099, "results": "243", "hashOfConfig": "205"}, {"size": 819, "mtime": 1748406934175, "results": "244", "hashOfConfig": "205"}, {"size": 804, "mtime": 1748406931614, "results": "245", "hashOfConfig": "205"}, {"size": 759, "mtime": 1748406938656, "results": "246", "hashOfConfig": "205"}, {"size": 762, "mtime": 1748406944863, "results": "247", "hashOfConfig": "205"}, {"size": 790, "mtime": 1748931700917, "results": "248", "hashOfConfig": "205"}, {"size": 1540, "mtime": 1748931717671, "results": "249", "hashOfConfig": "205"}, {"size": 822, "mtime": 1748931700911, "results": "250", "hashOfConfig": "205"}, {"size": 2502, "mtime": 1748931717672, "results": "251", "hashOfConfig": "205"}, {"size": 711, "mtime": 1748931717671, "results": "252", "hashOfConfig": "205"}, {"size": 3648, "mtime": 1748931717617, "results": "253", "hashOfConfig": "205"}, {"size": 10309, "mtime": 1748931717617, "results": "254", "hashOfConfig": "205"}, {"size": 5830, "mtime": 1748931717616, "results": "255", "hashOfConfig": "205"}, {"size": 14290, "mtime": 1748931700849, "results": "256", "hashOfConfig": "205"}, {"size": 2903, "mtime": 1749883647002, "results": "257", "hashOfConfig": "205"}, {"size": 8680, "mtime": 1747124732175, "results": "258", "hashOfConfig": "205"}, {"size": 8383, "mtime": 1748931717587, "results": "259", "hashOfConfig": "205"}, {"size": 10366, "mtime": 1748931673713, "results": "260", "hashOfConfig": "205"}, {"size": 9103, "mtime": 1748931700889, "results": "261", "hashOfConfig": "205"}, {"size": 7977, "mtime": 1748931717586, "results": "262", "hashOfConfig": "205"}, {"size": 428, "mtime": 1748931717639, "results": "263", "hashOfConfig": "205"}, {"size": 1744, "mtime": 1748931700840, "results": "264", "hashOfConfig": "205"}, {"size": 5371, "mtime": 1748931673690, "results": "265", "hashOfConfig": "205"}, {"size": 4605, "mtime": 1748931717572, "results": "266", "hashOfConfig": "205"}, {"size": 3891, "mtime": 1748407155402, "results": "267", "hashOfConfig": "205"}, {"size": 7136, "mtime": 1749881113570, "results": "268", "hashOfConfig": "205"}, {"size": 3839, "mtime": 1748407157768, "results": "269", "hashOfConfig": "205"}, {"size": 4440, "mtime": 1748577220703, "results": "270", "hashOfConfig": "205"}, {"size": 3614, "mtime": 1748931700844, "results": "271", "hashOfConfig": "205"}, {"size": 12083, "mtime": 1749883708572, "results": "272", "hashOfConfig": "205"}, {"size": 14918, "mtime": 1748931717573, "results": "273", "hashOfConfig": "205"}, {"size": 3509, "mtime": 1748931700840, "results": "274", "hashOfConfig": "205"}, {"size": 3545, "mtime": 1748931717575, "results": "275", "hashOfConfig": "205"}, {"size": 4806, "mtime": 1748931717574, "results": "276", "hashOfConfig": "205"}, {"size": 1767, "mtime": 1747124732210, "results": "277", "hashOfConfig": "205"}, {"size": 14741, "mtime": 1748931717610, "results": "278", "hashOfConfig": "205"}, {"size": 6154, "mtime": 1748931700872, "results": "279", "hashOfConfig": "205"}, {"size": 157, "mtime": 1748931717634, "results": "280", "hashOfConfig": "205"}, {"size": 2035, "mtime": 1748931717656, "results": "281", "hashOfConfig": "205"}, {"size": 982, "mtime": 1747124732082, "results": "282", "hashOfConfig": "205"}, {"size": 421, "mtime": 1747124732095, "results": "283", "hashOfConfig": "205"}, {"size": 31332, "mtime": 1749620217948, "results": "284", "hashOfConfig": "205"}, {"size": 810, "mtime": 1748577220695, "results": "285", "hashOfConfig": "205"}, {"size": 691, "mtime": 1747124732082, "results": "286", "hashOfConfig": "205"}, {"size": 1147, "mtime": 1748931700888, "results": "287", "hashOfConfig": "205"}, {"size": 11441, "mtime": 1748931717676, "results": "288", "hashOfConfig": "205"}, {"size": 1998, "mtime": 1748931717676, "results": "289", "hashOfConfig": "205"}, {"size": 9000, "mtime": 1748931717675, "results": "290", "hashOfConfig": "205"}, {"size": 852, "mtime": 1748412455335, "results": "291", "hashOfConfig": "205"}, {"size": 538, "mtime": 1747124732095, "results": "292", "hashOfConfig": "205"}, {"size": 887, "mtime": 1747124732095, "results": "293", "hashOfConfig": "205"}, {"size": 258, "mtime": 1748407111255, "results": "294", "hashOfConfig": "205"}, {"size": 418, "mtime": 1748931700894, "results": "295", "hashOfConfig": "205"}, {"size": 559, "mtime": 1747124732082, "results": "296", "hashOfConfig": "205"}, {"size": 4324, "mtime": 1748522688026, "results": "297", "hashOfConfig": "205"}, {"size": 8047, "mtime": 1748522688023, "results": "298", "hashOfConfig": "205"}, {"size": 1478, "mtime": 1747124732268, "results": "299", "hashOfConfig": "205"}, {"size": 13604, "mtime": 1748931717588, "results": "300", "hashOfConfig": "205"}, {"size": 1176, "mtime": 1748931717639, "results": "301", "hashOfConfig": "205"}, {"size": 6388, "mtime": 1748931673725, "results": "302", "hashOfConfig": "205"}, {"size": 7340, "mtime": 1748931673725, "results": "303", "hashOfConfig": "205"}, {"size": 5652, "mtime": 1748931673714, "results": "304", "hashOfConfig": "205"}, {"size": 765, "mtime": 1748931700892, "results": "305", "hashOfConfig": "205"}, {"size": 2107, "mtime": 1747124732082, "results": "306", "hashOfConfig": "205"}, {"size": 442, "mtime": 1747124732082, "results": "307", "hashOfConfig": "205"}, {"size": 2293, "mtime": 1747124732157, "results": "308", "hashOfConfig": "205"}, {"size": 4375, "mtime": 1749628571517, "results": "309", "hashOfConfig": "205"}, {"size": 1066, "mtime": 1747124732082, "results": "310", "hashOfConfig": "205"}, {"size": 1389, "mtime": 1748931717675, "results": "311", "hashOfConfig": "205"}, {"size": 103, "mtime": 1747124732095, "results": "312", "hashOfConfig": "205"}, {"size": 5107, "mtime": 1749628700868, "results": "313", "hashOfConfig": "205"}, {"size": 11215, "mtime": 1748931673712, "results": "314", "hashOfConfig": "205"}, {"size": 5279, "mtime": 1747124732263, "results": "315", "hashOfConfig": "205"}, {"size": 6649, "mtime": 1748931700869, "results": "316", "hashOfConfig": "205"}, {"size": 4086, "mtime": 1748931673727, "results": "317", "hashOfConfig": "205"}, {"size": 8457, "mtime": 1748931717607, "results": "318", "hashOfConfig": "205"}, {"size": 2827, "mtime": 1748931673728, "results": "319", "hashOfConfig": "205"}, {"size": 7765, "mtime": 1748931700871, "results": "320", "hashOfConfig": "205"}, {"size": 4366, "mtime": 1748931673729, "results": "321", "hashOfConfig": "205"}, {"size": 276, "mtime": 1748931673728, "results": "322", "hashOfConfig": "205"}, {"size": 8077, "mtime": 1748931717614, "results": "323", "hashOfConfig": "205"}, {"size": 8730, "mtime": 1748931717611, "results": "324", "hashOfConfig": "205"}, {"size": 935, "mtime": 1748931717638, "results": "325", "hashOfConfig": "205"}, {"size": 1299, "mtime": 1748407122979, "results": "326", "hashOfConfig": "205"}, {"size": 1420, "mtime": 1748407093178, "results": "327", "hashOfConfig": "205"}, {"size": 223, "mtime": 1748407072019, "results": "328", "hashOfConfig": "205"}, {"size": 1388, "mtime": 1748407090945, "results": "329", "hashOfConfig": "205"}, {"size": 1320, "mtime": 1748407095953, "results": "330", "hashOfConfig": "205"}, {"size": 570, "mtime": 1748407067480, "results": "331", "hashOfConfig": "205"}, {"size": 377, "mtime": 1748407074575, "results": "332", "hashOfConfig": "205"}, {"size": 720, "mtime": 1748407105341, "results": "333", "hashOfConfig": "205"}, {"size": 39922, "mtime": 1747124732095, "results": "334", "hashOfConfig": "205"}, {"size": 19835, "mtime": 1749883708580, "results": "335", "hashOfConfig": "205"}, {"size": 1214, "mtime": 1748931717652, "results": "336", "hashOfConfig": "205"}, {"size": 6427, "mtime": 1749883708585, "results": "337", "hashOfConfig": "205"}, {"size": 1904, "mtime": 1749883708585, "results": "338", "hashOfConfig": "205"}, {"size": 6225, "mtime": 1749883708581, "results": "339", "hashOfConfig": "205"}, {"size": 11619, "mtime": 1749883708584, "results": "340", "hashOfConfig": "205"}, {"size": 9695, "mtime": 1749883647020, "results": "341", "hashOfConfig": "205"}, {"size": 7851, "mtime": 1748931700873, "results": "342", "hashOfConfig": "205"}, {"size": 2762, "mtime": 1748522688462, "results": "343", "hashOfConfig": "205"}, {"size": 7115, "mtime": 1747124732210, "results": "344", "hashOfConfig": "205"}, {"size": 6657, "mtime": 1748522688462, "results": "345", "hashOfConfig": "205"}, {"size": 425, "mtime": 1749892531655, "results": "346", "hashOfConfig": "205"}, {"size": 1292, "mtime": 1749883708587, "results": "347", "hashOfConfig": "205"}, {"size": 19730, "mtime": 1749897894520, "results": "348", "hashOfConfig": "205"}, {"size": 3443, "mtime": 1747124732268, "results": "349", "hashOfConfig": "205"}, {"size": 4977, "mtime": 1747124732263, "results": "350", "hashOfConfig": "205"}, {"size": 6426, "mtime": 1748522688413, "results": "351", "hashOfConfig": "205"}, {"size": 207, "mtime": 1748931717608, "results": "352", "hashOfConfig": "205"}, {"size": 853, "mtime": 1747124732167, "results": "353", "hashOfConfig": "205"}, {"size": 25088, "mtime": 1749883708579, "results": "354", "hashOfConfig": "205"}, {"size": 13333, "mtime": 1748577220725, "results": "355", "hashOfConfig": "205"}, {"size": 13294, "mtime": 1748931717619, "results": "356", "hashOfConfig": "205"}, {"size": 4868, "mtime": 1748931717620, "results": "357", "hashOfConfig": "205"}, {"size": 6593, "mtime": 1749897972133, "results": "358", "hashOfConfig": "205"}, {"size": 2025, "mtime": 1748931717642, "results": "359", "hashOfConfig": "205"}, {"size": 739, "mtime": 1748931717644, "results": "360", "hashOfConfig": "205"}, {"size": 3197, "mtime": 1749883708586, "results": "361", "hashOfConfig": "205"}, {"size": 1620, "mtime": 1749883708581, "results": "362", "hashOfConfig": "205"}, {"size": 693, "mtime": 1749883708582, "results": "363", "hashOfConfig": "205"}, {"size": 634, "mtime": 1749883708583, "results": "364", "hashOfConfig": "205"}, {"size": 675, "mtime": 1749883708583, "results": "365", "hashOfConfig": "205"}, {"size": 185, "mtime": 1747124732095, "results": "366", "hashOfConfig": "205"}, {"size": 2375, "mtime": 1749883708582, "results": "367", "hashOfConfig": "205"}, {"size": 19078, "mtime": 1749883647036, "results": "368", "hashOfConfig": "205"}, {"size": 15339, "mtime": 1748931717623, "results": "369", "hashOfConfig": "205"}, {"size": 20031, "mtime": 1748931717622, "results": "370", "hashOfConfig": "205"}, {"size": 5352, "mtime": 1749628571486, "results": "371", "hashOfConfig": "205"}, {"size": 2880, "mtime": 1749628571441, "results": "372", "hashOfConfig": "205"}, {"size": 4065, "mtime": 1749628571458, "results": "373", "hashOfConfig": "205"}, {"size": 3060, "mtime": 1748931717582, "results": "374", "hashOfConfig": "205"}, {"size": 27946, "mtime": 1749883646989, "results": "375", "hashOfConfig": "205"}, {"size": 6582, "mtime": 1748931717585, "results": "376", "hashOfConfig": "205"}, {"size": 5723, "mtime": 1749628571471, "results": "377", "hashOfConfig": "205"}, {"size": 8635, "mtime": 1748931673707, "results": "378", "hashOfConfig": "205"}, {"size": 1364, "mtime": 1749883646955, "results": "379", "hashOfConfig": "205"}, {"size": 1849, "mtime": 1749883708586, "results": "380", "hashOfConfig": "205"}, {"size": 272, "mtime": 1749714907747, "results": "381", "hashOfConfig": "205"}, {"size": 532, "mtime": 1749883646956, "results": "382", "hashOfConfig": "205"}, {"size": 4253, "mtime": 1749890030051, "results": "383", "hashOfConfig": "205"}, {"size": 131, "mtime": 1749883708577, "results": "384", "hashOfConfig": "205"}, {"size": 5121, "mtime": 1749883708577, "results": "385", "hashOfConfig": "205"}, {"size": 5407, "mtime": 1749883708572, "results": "386", "hashOfConfig": "205"}, {"size": 4120, "mtime": 1749883646964, "results": "387", "hashOfConfig": "205"}, {"size": 1576, "mtime": 1749883646964, "results": "388", "hashOfConfig": "205"}, {"size": 12041, "mtime": 1749883708573, "results": "389", "hashOfConfig": "205"}, {"size": 8608, "mtime": 1749883708574, "results": "390", "hashOfConfig": "205"}, {"size": 6003, "mtime": 1749883708574, "results": "391", "hashOfConfig": "205"}, {"size": 5938, "mtime": 1749883708576, "results": "392", "hashOfConfig": "205"}, {"size": 6755, "mtime": 1749883708575, "results": "393", "hashOfConfig": "205"}, {"size": 5805, "mtime": 1749883708576, "results": "394", "hashOfConfig": "205"}, {"size": 6003, "mtime": 1749883708574, "results": "395", "hashOfConfig": "205"}, {"size": 12041, "mtime": 1749883708573, "results": "396", "hashOfConfig": "205"}, {"size": 11461, "mtime": 1750152312165, "results": "397", "hashOfConfig": "205"}, {"size": 7384, "mtime": 1750142871203, "results": "398", "hashOfConfig": "205"}, {"size": 7997, "mtime": 1750144301220, "results": "399", "hashOfConfig": "205"}, {"size": 8608, "mtime": 1749889937971, "results": "400", "hashOfConfig": "205"}, {"size": 1174, "mtime": 1750149219809, "results": "401", "hashOfConfig": "205"}, {"size": 3067, "mtime": 1749892550396, "results": "402", "hashOfConfig": "205"}, {"size": 9676, "mtime": 1750152010512, "results": "403", "hashOfConfig": "205"}, {"size": 13290, "mtime": 1750152036594, "results": "404", "hashOfConfig": "205"}, {"size": 16951, "mtime": 1750152519709, "results": "405", "hashOfConfig": "205"}, {"size": 7832, "mtime": 1750150527664, "results": "406", "hashOfConfig": "205"}, {"size": 11738, "mtime": 1750152658970, "results": "407", "hashOfConfig": "205"}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "utrof9", {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\reportWebVitals.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\store.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\routes.js", ["1017"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\theme.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\reducers.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Backgroundprovider.jsx", ["1018", "1019"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Create.js", ["1020"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\MainLayout.js", ["1021", "1022"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\AuthLayout.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Auth\\Login.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PermissionRoute.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveApproval\\Approval.jsx", ["1023", "1024"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\config\\permissionConfig.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\permission.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveConfiguration\\LeaveConfiguration.jsx", ["1025", "1026"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveReport\\LeaveReport.jsx", ["1027"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Tasklist.jsx", ["1028"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveCalendar\\LeaveCalendar.jsx", ["1029"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductList.jsx", ["1030"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TaskHistoryAdmin.jsx", ["1031"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductTimesheet.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\NotFound.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductOverview.jsx", ["1032"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\AccessDenied.jsx", ["1033"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Client\\Client.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\ProductListStaff.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\TaskListWithNote.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\GeneralSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AuthSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\UserSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AttendanceSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DesignationSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DepartmentSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ExpensesSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\LeaveSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\TimelineSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ProductSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ClientSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SprintSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SettingSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\UserSprintPage.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintTasksPage.jsx", ["1034", "1035"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintPage.jsx", ["1036", "1037"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\index.js", ["1038"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AuthSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\UserSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DepartmentSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DesignationSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AttendanceSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ExpenseSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\LeaveSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\TimelineSaga.js", ["1039"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ActivitySaga.js", ["1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ProductSaga.js", ["1050", "1051"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ClientSaga.js", ["1052", "1053", "1054"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SprintSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SettingSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Setting\\index.js", ["1055"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\index.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\actions.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PageTitle.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\role.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Permission.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Input.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\SelectField.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\constants\\menus.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\menuGenerator.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\permissionLogger.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\can.js", ["1056"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\FormSkeleton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\sort.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\leaveConst.js", ["1057"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SettingSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\TimelineSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\FloatingButton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\MenuForm.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductAdd.jsx", ["1058"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SprintSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TimesheetFilters.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\DateRangeSelector.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ProductSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\CustomMenu.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\ListSkeleton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Charts.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widgets.js", ["1059", "1060", "1061", "1062", "1063"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\DialogConfirm.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\convertion.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\expenseStatus.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\UserLeaveInfo.jsx", ["1064"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\LeaveCalender.jsx", ["1065", "1066"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Filter.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskFilterUser.jsx", ["1067"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\AddMembers.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskHeader.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeader.jsx", ["1068", "1069"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskInfoComponent.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeaderFilterStaff.jsx", ["1070"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\Note.jsx", ["1071"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintList.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintForm.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\GeneralSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\UserSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DesignationSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AuthSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DepartmentSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ExpensesSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AttendanceSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ClientSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\LeaveSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\countries.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\TimelineNew.jsx", ["1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SettingService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SprintService.js", ["1088", "1089"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\TimelineService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ActivityService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ProductService.js", ["1090"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\EmployeeList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\AttendanceList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\ExpensesList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\apiConfig.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Leave.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Attendance.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\constant\\ProductConts.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widget.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Activity.js", ["1091", "1092"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\FormLeavePop.jsx", ["1093", "1094", "1095"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\api.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AuthService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DepartmentService.js", ["1096"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\UserService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AttendanceService.js", ["1097"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DesignationService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ExpensesService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\LeaveService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\gender.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ClientService.js", ["1098"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\WeekView.jsx", ["1099", "1100", "1101", "1102"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\MonthView.jsx", ["1103", "1104", "1105", "1106"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\DayView.jsx", ["1107", "1108", "1109"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TodayGoal.jsx", ["1110", "1111", "1112", "1113"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\BreakReasone.jsx", ["1114", "1115"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\EarlyLate.jsx", ["1116", "1117"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\AttendanceBarChart.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\ProductivityChart.jsx", ["1118", "1119", "1120", "1121", "1122"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\WorkHoursStatus.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\OverLimitBreak.jsx", ["1123"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TimelineRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\App.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ActivitySlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ActivitySelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\LoadingScreen.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\WorkSchedule.jsx", ["1124", "1125", "1126"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TaskRequest\\TaskRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TimeRequest\\TimeRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\Overview.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\AdminDashboard.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\UserDashboard.js", ["1127"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\MonthlyWorkReport.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\DayWorkReport.jsx", ["1128"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\WeekWorkReport.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\WeekWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\workSchedule.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\WorkScheduleService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkScheduleForm.jsx", ["1129"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkScheduleForm.jsx", ["1130"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\AdvancedWorkScheduleForm.jsx", ["1131"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\workScheduleUtils.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\ScheduleManager.jsx", ["1132"], [], {"ruleId": "1133", "severity": 1, "message": "1134", "line": 390, "column": 12, "nodeType": "1135", "messageId": "1136", "endLine": 390, "endColumn": 20}, {"ruleId": "1133", "severity": 1, "message": "1137", "line": 1, "column": 21, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 28}, {"ruleId": "1133", "severity": 1, "message": "1138", "line": 18, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 18, "endColumn": 24}, {"ruleId": "1133", "severity": 1, "message": "1139", "line": 21, "column": 8, "nodeType": "1135", "messageId": "1136", "endLine": 21, "endColumn": 18}, {"ruleId": "1133", "severity": 1, "message": "1140", "line": 31, "column": 8, "nodeType": "1135", "messageId": "1136", "endLine": 31, "endColumn": 11}, {"ruleId": "1133", "severity": 1, "message": "1141", "line": 36, "column": 8, "nodeType": "1135", "messageId": "1136", "endLine": 36, "endColumn": 13}, {"ruleId": "1133", "severity": 1, "message": "1142", "line": 14, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 14, "endColumn": 7}, {"ruleId": "1133", "severity": 1, "message": "1143", "line": 40, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 40, "endColumn": 14}, {"ruleId": "1133", "severity": 1, "message": "1142", "line": 14, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 14, "endColumn": 7}, {"ruleId": "1133", "severity": 1, "message": "1144", "line": 16, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 16, "endColumn": 20}, {"ruleId": "1133", "severity": 1, "message": "1145", "line": 66, "column": 8, "nodeType": "1135", "messageId": "1136", "endLine": 66, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1146", "line": 15, "column": 7, "nodeType": "1135", "messageId": "1136", "endLine": 15, "endColumn": 16}, {"ruleId": "1133", "severity": 1, "message": "1137", "line": 9, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 9, "endColumn": 17}, {"ruleId": "1133", "severity": 1, "message": "1146", "line": 27, "column": 7, "nodeType": "1135", "messageId": "1136", "endLine": 27, "endColumn": 16}, {"ruleId": "1133", "severity": 1, "message": "1147", "line": 46, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 46, "endColumn": 16}, {"ruleId": "1133", "severity": 1, "message": "1148", "line": 204, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 204, "endColumn": 20}, {"ruleId": "1133", "severity": 1, "message": "1149", "line": 5, "column": 8, "nodeType": "1135", "messageId": "1136", "endLine": 5, "endColumn": 24}, {"ruleId": "1133", "severity": 1, "message": "1150", "line": 22, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 22, "endColumn": 10}, {"ruleId": "1133", "severity": 1, "message": "1151", "line": 36, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 36, "endColumn": 20}, {"ruleId": "1133", "severity": 1, "message": "1152", "line": 34, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 34, "endColumn": 17}, {"ruleId": "1133", "severity": 1, "message": "1153", "line": 131, "column": 7, "nodeType": "1135", "messageId": "1136", "endLine": 131, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1154", "line": 50, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 50, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1155", "line": 11, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 11, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1155", "line": 11, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 11, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 95, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 95, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 117, "column": 13, "nodeType": "1135", "messageId": "1136", "endLine": 117, "endColumn": 17}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 143, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 143, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 165, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 165, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 186, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 186, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1155", "line": 206, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 206, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 227, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 227, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 248, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 248, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 269, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 269, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1155", "line": 45, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 45, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1156", "line": 92, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 92, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1155", "line": 12, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 12, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1155", "line": 33, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 33, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1155", "line": 55, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 55, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1157", "line": 8, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 8, "endColumn": 24}, {"ruleId": "1133", "severity": 1, "message": "1158", "line": 7, "column": 7, "nodeType": "1135", "messageId": "1136", "endLine": 7, "endColumn": 10}, {"ruleId": "1133", "severity": 1, "message": "1159", "line": 1, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 16}, {"ruleId": "1133", "severity": 1, "message": "1137", "line": 2, "column": 21, "nodeType": "1135", "messageId": "1136", "endLine": 2, "endColumn": 28}, {"ruleId": "1133", "severity": 1, "message": "1160", "line": 4, "column": 31, "nodeType": "1135", "messageId": "1136", "endLine": 4, "endColumn": 52}, {"ruleId": "1133", "severity": 1, "message": "1161", "line": 8, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 8, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1162", "line": 23, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 23, "endColumn": 18}, {"ruleId": "1133", "severity": 1, "message": "1163", "line": 24, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 24, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1164", "line": 25, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 25, "endColumn": 18}, {"ruleId": "1133", "severity": 1, "message": "1165", "line": 16, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 16, "endColumn": 24}, {"ruleId": "1133", "severity": 1, "message": "1166", "line": 1, "column": 17, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 27}, {"ruleId": "1133", "severity": 1, "message": "1167", "line": 3, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 3, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1152", "line": 25, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 25, "endColumn": 17}, {"ruleId": "1133", "severity": 1, "message": "1168", "line": 2, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 2, "endColumn": 20}, {"ruleId": "1133", "severity": 1, "message": "1137", "line": 12, "column": 21, "nodeType": "1135", "messageId": "1136", "endLine": 12, "endColumn": 28}, {"ruleId": "1133", "severity": 1, "message": "1169", "line": 1, "column": 42, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 51}, {"ruleId": "1133", "severity": 1, "message": "1142", "line": 1, "column": 16, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 20}, {"ruleId": "1133", "severity": 1, "message": "1170", "line": 3, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 3, "endColumn": 8}, {"ruleId": "1133", "severity": 1, "message": "1169", "line": 3, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 3, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1171", "line": 3, "column": 21, "nodeType": "1135", "messageId": "1136", "endLine": 3, "endColumn": 30}, {"ruleId": "1133", "severity": 1, "message": "1172", "line": 3, "column": 32, "nodeType": "1135", "messageId": "1136", "endLine": 3, "endColumn": 46}, {"ruleId": "1133", "severity": 1, "message": "1173", "line": 3, "column": 48, "nodeType": "1135", "messageId": "1136", "endLine": 3, "endColumn": 57}, {"ruleId": "1133", "severity": 1, "message": "1174", "line": 3, "column": 59, "nodeType": "1135", "messageId": "1136", "endLine": 3, "endColumn": 67}, {"ruleId": "1133", "severity": 1, "message": "1175", "line": 3, "column": 69, "nodeType": "1135", "messageId": "1136", "endLine": 3, "endColumn": 74}, {"ruleId": "1133", "severity": 1, "message": "1176", "line": 4, "column": 16, "nodeType": "1135", "messageId": "1136", "endLine": 4, "endColumn": 27}, {"ruleId": "1133", "severity": 1, "message": "1177", "line": 4, "column": 39, "nodeType": "1135", "messageId": "1136", "endLine": 4, "endColumn": 45}, {"ruleId": "1133", "severity": 1, "message": "1178", "line": 4, "column": 47, "nodeType": "1135", "messageId": "1136", "endLine": 4, "endColumn": 58}, {"ruleId": "1133", "severity": 1, "message": "1179", "line": 4, "column": 60, "nodeType": "1135", "messageId": "1136", "endLine": 4, "endColumn": 73}, {"ruleId": "1133", "severity": 1, "message": "1180", "line": 4, "column": 75, "nodeType": "1135", "messageId": "1136", "endLine": 4, "endColumn": 88}, {"ruleId": "1133", "severity": 1, "message": "1181", "line": 5, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 5, "endColumn": 13}, {"ruleId": "1133", "severity": 1, "message": "1182", "line": 5, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 5, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1183", "line": 5, "column": 23, "nodeType": "1135", "messageId": "1136", "endLine": 5, "endColumn": 30}, {"ruleId": "1133", "severity": 1, "message": "1184", "line": 30, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 30, "endColumn": 14}, {"ruleId": "1133", "severity": 1, "message": "1185", "line": 1, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 22}, {"ruleId": "1133", "severity": 1, "message": "1186", "line": 14, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 14, "endColumn": 26}, {"ruleId": "1133", "severity": 1, "message": "1185", "line": 1, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 22}, {"ruleId": "1133", "severity": 1, "message": "1187", "line": 17, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 17, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1138", "line": 67, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 67, "endColumn": 24}, {"ruleId": "1133", "severity": 1, "message": "1188", "line": 6, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 6, "endColumn": 20}, {"ruleId": "1133", "severity": 1, "message": "1189", "line": 17, "column": 39, "nodeType": "1135", "messageId": "1136", "endLine": 17, "endColumn": 50}, {"ruleId": "1133", "severity": 1, "message": "1190", "line": 21, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 21, "endColumn": 20}, {"ruleId": "1133", "severity": 1, "message": "1191", "line": 2, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 2, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1192", "line": 1, "column": 14, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 18}, {"ruleId": "1133", "severity": 1, "message": "1185", "line": 1, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 22}, {"ruleId": "1133", "severity": 1, "message": "1172", "line": 8, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 8, "endColumn": 17}, {"ruleId": "1133", "severity": 1, "message": "1175", "line": 11, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 11, "endColumn": 8}, {"ruleId": "1133", "severity": 1, "message": "1193", "line": 67, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 67, "endColumn": 15}, {"ruleId": "1133", "severity": 1, "message": "1194", "line": 113, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 113, "endColumn": 31}, {"ruleId": "1133", "severity": 1, "message": "1195", "line": 8, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 8, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1196", "line": 75, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 75, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1194", "line": 283, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 283, "endColumn": 31}, {"ruleId": "1133", "severity": 1, "message": "1197", "line": 422, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 422, "endColumn": 22}, {"ruleId": "1133", "severity": 1, "message": "1172", "line": 9, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 9, "endColumn": 17}, {"ruleId": "1133", "severity": 1, "message": "1175", "line": 12, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 12, "endColumn": 8}, {"ruleId": "1133", "severity": 1, "message": "1194", "line": 98, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 98, "endColumn": 31}, {"ruleId": "1133", "severity": 1, "message": "1198", "line": 11, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 11, "endColumn": 25}, {"ruleId": "1133", "severity": 1, "message": "1199", "line": 24, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 24, "endColumn": 23}, {"ruleId": "1133", "severity": 1, "message": "1200", "line": 25, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 25, "endColumn": 24}, {"ruleId": "1133", "severity": 1, "message": "1201", "line": 28, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 28, "endColumn": 17}, {"ruleId": "1133", "severity": 1, "message": "1202", "line": 1, "column": 36, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 43}, {"ruleId": "1133", "severity": 1, "message": "1203", "line": 5, "column": 22, "nodeType": "1135", "messageId": "1136", "endLine": 5, "endColumn": 33}, {"ruleId": "1133", "severity": 1, "message": "1203", "line": 10, "column": 23, "nodeType": "1135", "messageId": "1136", "endLine": 10, "endColumn": 34}, {"ruleId": "1133", "severity": 1, "message": "1204", "line": 34, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 34, "endColumn": 19}, {"ruleId": "1133", "severity": 1, "message": "1166", "line": 14, "column": 38, "nodeType": "1135", "messageId": "1136", "endLine": 14, "endColumn": 48}, {"ruleId": "1133", "severity": 1, "message": "1167", "line": 18, "column": 10, "nodeType": "1135", "messageId": "1136", "endLine": 18, "endColumn": 21}, {"ruleId": "1133", "severity": 1, "message": "1205", "line": 412, "column": 25, "nodeType": "1135", "messageId": "1136", "endLine": 412, "endColumn": 32}, {"ruleId": "1133", "severity": 1, "message": "1206", "line": 417, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 417, "endColumn": 33}, {"ruleId": "1133", "severity": 1, "message": "1207", "line": 419, "column": 15, "nodeType": "1135", "messageId": "1136", "endLine": 419, "endColumn": 31}, {"ruleId": "1133", "severity": 1, "message": "1199", "line": 23, "column": 12, "nodeType": "1135", "messageId": "1136", "endLine": 23, "endColumn": 25}, {"ruleId": "1133", "severity": 1, "message": "1208", "line": 1, "column": 27, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 36}, {"ruleId": "1133", "severity": 1, "message": "1209", "line": 33, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 33, "endColumn": 28}, {"ruleId": "1133", "severity": 1, "message": "1210", "line": 56, "column": 9, "nodeType": "1135", "messageId": "1136", "endLine": 56, "endColumn": 23}, {"ruleId": "1133", "severity": 1, "message": "1162", "line": 13, "column": 11, "nodeType": "1135", "messageId": "1136", "endLine": 13, "endColumn": 18}, {"ruleId": "1133", "severity": 1, "message": "1211", "line": 23, "column": 8, "nodeType": "1135", "messageId": "1136", "endLine": 23, "endColumn": 18}, {"ruleId": "1133", "severity": 1, "message": "1212", "line": 1, "column": 17, "nodeType": "1135", "messageId": "1136", "endLine": 1, "endColumn": 25}, {"ruleId": "1133", "severity": 1, "message": "1213", "line": 21, "column": 30, "nodeType": "1135", "messageId": "1136", "endLine": 21, "endColumn": 44}, {"ruleId": "1133", "severity": 1, "message": "1214", "line": 12, "column": 3, "nodeType": "1135", "messageId": "1136", "endLine": 12, "endColumn": 7}, {"ruleId": "1133", "severity": 1, "message": "1215", "line": 20, "column": 24, "nodeType": "1135", "messageId": "1136", "endLine": 20, "endColumn": 27}, "no-unused-vars", "'userRole' is assigned a value but never used.", "Identifier", "unusedVar", "'element' is defined but never used.", "'slotController' is assigned a value but never used.", "'Permission' is defined but never used.", "'Can' is defined but never used.", "'ROLES' is defined but never used.", "'Grid' is defined but never used.", "'users' is assigned a value but never used.", "'DialogContentText' is defined but never used.", "'daysOfMonth' is assigned a value but never used.", "'FilterBox' is assigned a value but never used.", "'history' is assigned a value but never used.", "'totalSpent' is assigned a value but never used.", "'ErrorOutlineIcon' is defined but never used.", "'Divider' is defined but never used.", "'currentUser' is assigned a value but never used.", "'products' is assigned a value but never used.", "'isMySprintPage' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'result' is assigned a value but never used.", "'data' is assigned a value but never used.", "'SettingSelector' is defined but never used.", "'log' is assigned a value but never used.", "'values' is defined but never used.", "'AssignmentIndOutlined' is defined but never used.", "'LeaveActions' is defined but never used.", "'profile' is assigned a value but never used.", "'countLeave' is assigned a value but never used.", "'setting' is assigned a value but never used.", "'upcomingLeaves' is assigned a value but never used.", "'useContext' is defined but never used.", "'backContext' is defined but never used.", "'PlayCircle' is defined but never used.", "'TableBody' is defined but never used.", "'Table' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "'ButtonGroup' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'IconButton' is defined but never used.", "'Popper' is defined but never used.", "'Popover' is defined but never used.", "'theme' is assigned a value but never used.", "'API_BASE_URL' is defined but never used.", "'queryParams' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "'InputBase' is defined but never used.", "'UserActions' is defined but never used.", "'LeaveStatus' is defined but never used.", "'getApiUrl' is defined but never used.", "'post' is defined but never used.", "'totals' is assigned a value but never used.", "'formatTimeForTooltip' is assigned a value but never used.", "'monthData' is assigned a value but never used.", "'getMonthName' is assigned a value but never used.", "'monthlyTotals' is assigned a value but never used.", "'ActivityActions' is defined but never used.", "'todayActivity' is assigned a value but never used.", "'todayHistoryArr' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'useMemo' is defined but never used.", "'useSelector' is defined but never used.", "'body' is assigned a value but never used.", "'endHour' is assigned a value but never used.", "'startTimeFormatted' is assigned a value but never used.", "'endTimeFormatted' is assigned a value but never used.", "'useEffect' is defined but never used.", "'getCurrentDateRange' is assigned a value but never used.", "'lastFetchedRef' is assigned a value but never used.", "'formatTime' is assigned a value but never used.", "'useState' is defined but never used.", "'SCHEDULE_TYPES' is defined but never used.", "'Chip' is defined but never used.", "'Add' is defined but never used."]