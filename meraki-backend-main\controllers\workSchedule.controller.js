'use strict';

/**
 * Work Schedule Controller
 *
 * This controller handles work schedule-related operations including:
 * - User work schedule management
 * - Work schedule templates
 * - Work schedule validation
 */

const WorkScheduleService = require("../services/workSchedule.service");
const UserService = require("../services/user.service");

/**
 * Get user's work schedule
 *
 * @param {Object} req - Express request object with userId in params
 * @param {Object} res - Express response object
 * @returns {Object} Response with user's work schedule or error
 */
exports.getUserWorkSchedule = async (req, res) => {
    try {
        const { userId } = req.params;

        // Get user with work schedule
        const user = await UserService.getUserById(userId);
        if (!user) {
            return res.status(404).send({
                message: "User not found"
            });
        }

        return res.status(200).send({
            message: "Work schedule retrieved successfully",
            data: user.workSchedule
        });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to retrieve work schedule",
            error: error.message
        });
    }
};

/**
 * Update user's work schedule
 *
 * @param {Object} req - Express request object with userId in params and work schedule data in body
 * @param {Object} res - Express response object
 * @returns {Object} Response with success message or error
 */
exports.updateUserWorkSchedule = async (req, res) => {
    try {
        const { userId } = req.params;
        const { workSchedule } = req.body;

        // Validate work schedule data
        const validationResult = WorkScheduleService.validateWorkSchedule(workSchedule);
        if (!validationResult.isValid) {
            return res.status(400).send({
                message: "Invalid work schedule data",
                errors: validationResult.errors
            });
        }

        // Update user's work schedule
        const result = await UserService.updateUser(userId, { workSchedule });
        if (!result || result.n === 0) {
            return res.status(404).send({
                message: "User not found or update failed"
            });
        }

        return res.status(200).send({
            message: "Work schedule updated successfully"
        });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to update work schedule",
            error: error.message
        });
    }
};

/**
 * Get work schedule templates
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Response with work schedule templates or error
 */
exports.getWorkScheduleTemplates = async (req, res) => {
    try {
        const templates = await WorkScheduleService.getWorkScheduleTemplates();
        
        return res.status(200).send({
            message: "Work schedule templates retrieved successfully",
            data: templates
        });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to retrieve work schedule templates",
            error: error.message
        });
    }
};

/**
 * Create work schedule template
 *
 * @param {Object} req - Express request object with template data in body
 * @param {Object} res - Express response object
 * @returns {Object} Response with created template or error
 */
exports.createWorkScheduleTemplate = async (req, res) => {
    try {
        const templateData = req.body;

        // Validate template data
        const validationResult = WorkScheduleService.validateWorkScheduleTemplate(templateData);
        if (!validationResult.isValid) {
            return res.status(400).send({
                message: "Invalid template data",
                errors: validationResult.errors
            });
        }

        const template = await WorkScheduleService.createWorkScheduleTemplate(templateData);
        
        return res.status(201).send({
            message: "Work schedule template created successfully",
            data: template
        });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to create work schedule template",
            error: error.message
        });
    }
};

/**
 * Update work schedule template
 *
 * @param {Object} req - Express request object with templateId in params and template data in body
 * @param {Object} res - Express response object
 * @returns {Object} Response with success message or error
 */
exports.updateWorkScheduleTemplate = async (req, res) => {
    try {
        const { templateId } = req.params;
        const templateData = req.body;

        // Validate template data
        const validationResult = WorkScheduleService.validateWorkScheduleTemplate(templateData);
        if (!validationResult.isValid) {
            return res.status(400).send({
                message: "Invalid template data",
                errors: validationResult.errors
            });
        }

        const result = await WorkScheduleService.updateWorkScheduleTemplate(templateId, templateData);
        if (!result) {
            return res.status(404).send({
                message: "Template not found or update failed"
            });
        }

        return res.status(200).send({
            message: "Work schedule template updated successfully"
        });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to update work schedule template",
            error: error.message
        });
    }
};

/**
 * Delete work schedule template
 *
 * @param {Object} req - Express request object with templateId in params
 * @param {Object} res - Express response object
 * @returns {Object} Response with success message or error
 */
exports.deleteWorkScheduleTemplate = async (req, res) => {
    try {
        const { templateId } = req.params;

        const result = await WorkScheduleService.deleteWorkScheduleTemplate(templateId);
        if (!result) {
            return res.status(404).send({
                message: "Template not found or deletion failed"
            });
        }

        return res.status(200).send({
            message: "Work schedule template deleted successfully"
        });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to delete work schedule template",
            error: error.message
        });
    }
};
