import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  MenuItem,
  Box,
  Chip,
  FormControlLabel,
  Checkbox,
  Alert
} from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import Input from 'components/Input';
import SelectField from 'components/SelectField';
import { UserActions, GeneralActions } from 'slices/actions';
import { GeneralSelector, UserSelector } from 'selectors';
import { SCHEDULE_TEMPLATES, SCHEDULE_TYPES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';
import WorkScheduleUtils from 'utils/workScheduleUtils';

const AdvancedWorkScheduleForm = ({ open, onClose, selectedUser, selectedDate, scheduleType = 'daily', editingSchedule = null }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));
  const [currentSelectedUser, setCurrentSelectedUser] = useState(selectedUser);
  const [previewSchedule, setPreviewSchedule] = useState(null);

  useEffect(() => {
    if (success) {
      toast.success(`Work schedule ${editingSchedule ? 'updated' : 'created'} successfully!`, {
        position: "top-right",
        autoClose: 3000,
        closeOnClick: true,
      });
      onClose();
      // Reset success state to prevent multiple alerts
      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));
    }
  }, [success, onClose, dispatch, editingSchedule]);

  useEffect(() => {
    setCurrentSelectedUser(selectedUser);
  }, [selectedUser]);

  // Helper function to format date for input field
  const formatDateForInput = (dateValue) => {
    if (!dateValue) {
      return new Date().toISOString().split('T')[0];
    }
    if (typeof dateValue === 'string') {
      return dateValue.split('T')[0];
    }
    return new Date(dateValue).toISOString().split('T')[0];
  };

  const formik = useFormik({
    initialValues: {
      selectedUserId: editingSchedule?.userId || currentSelectedUser?._id || (users.length > 0 ? users[0]._id : ''),
      type: editingSchedule?.type || scheduleType,
      scheduleTemplate: editingSchedule?.scheduleTemplate || currentSelectedUser?.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate,
      startTime: editingSchedule?.startTime || currentSelectedUser?.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime,
      endTime: editingSchedule?.endTime || currentSelectedUser?.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime,
      minimumHours: editingSchedule?.minimumHours || currentSelectedUser?.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours,
      effectiveFrom: editingSchedule?.effectiveFrom || formatDateForInput(selectedDate),
      effectiveTo: editingSchedule?.effectiveTo || formatDateForInput(selectedDate),
      specificDate: editingSchedule?.specificDate || formatDateForInput(selectedDate),
      daysOfWeek: editingSchedule?.daysOfWeek || [1, 2, 3, 4, 5], // Monday to Friday
      description: editingSchedule?.description || '',
      isRecurring: false
    },
    enableReinitialize: true,
    onSubmit: (values) => {
      handleSubmit(values);
    }
  });

  const handleSubmit = (values) => {
    const targetUser = users.find(u => u._id === values.selectedUserId);
    if (!targetUser) {
      toast.error('Please select a valid user');
      return;
    }

    let updatedWorkSchedules;

    if (editingSchedule) {
      // Update existing schedule
      updatedWorkSchedules = (targetUser.workSchedules || []).map(schedule =>
        schedule.id === editingSchedule.id ? {
          ...schedule,
          type: values.type,
          scheduleTemplate: values.scheduleTemplate,
          startTime: values.startTime,
          endTime: values.endTime,
          minimumHours: parseFloat(values.minimumHours),
          effectiveFrom: values.effectiveFrom,
          effectiveTo: values.effectiveTo,
          specificDate: values.type === 'daily' || values.type === 'time_specific' ? values.specificDate : null,
          daysOfWeek: values.type === 'weekly' ? values.daysOfWeek : null,
          description: values.description,
          priority: WorkScheduleUtils.getSchedulePriority(values.type)
        } : schedule
      );
    } else {
      // Create new schedule entry
      const scheduleEntry = WorkScheduleUtils.createScheduleEntry(targetUser._id, {
        type: values.type,
        scheduleTemplate: values.scheduleTemplate,
        startTime: values.startTime,
        endTime: values.endTime,
        minimumHours: parseFloat(values.minimumHours),
        effectiveFrom: values.effectiveFrom,
        effectiveTo: values.effectiveTo,
        specificDate: values.type === 'daily' || values.type === 'time_specific' ? values.specificDate : null,
        daysOfWeek: values.type === 'weekly' ? values.daysOfWeek : null,
        description: values.description
      });

      updatedWorkSchedules = [...(targetUser.workSchedules || []), scheduleEntry];
    }

    const params = {
      id: targetUser._id,
      workSchedules: updatedWorkSchedules
    };

    dispatch(UserActions.updateUser(params));
  };

  const handleWorkScheduleChange = (field, value) => {
    formik.setFieldValue(field, value);
    
    // If user selection changes, update the form with that user's current schedule
    if (field === 'selectedUserId') {
      const selectedUser = users.find(u => u._id === value);
      if (selectedUser) {
        setCurrentSelectedUser(selectedUser);
        formik.setFieldValue('scheduleTemplate', selectedUser.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate);
        formik.setFieldValue('startTime', selectedUser.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime);
        formik.setFieldValue('endTime', selectedUser.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime);
        formik.setFieldValue('minimumHours', selectedUser.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours);
      }
    }
    
    // Auto-calculate hours when start or end time changes
    if (field === 'startTime' || field === 'endTime') {
      const startTime = field === 'startTime' ? value : formik.values.startTime;
      const endTime = field === 'endTime' ? value : formik.values.endTime;
      
      if (startTime && endTime) {
        const calculatedHours = WorkScheduleUtils.calculateHours(startTime, endTime);
        formik.setFieldValue('minimumHours', calculatedHours);
      }
    }
    
    // Auto-suggest schedule template based on time
    if (field === 'startTime') {
      const hour = parseInt(value.split(':')[0], 10);
      if (hour >= 22 || hour < 6) {
        formik.setFieldValue('scheduleTemplate', 'night_shift');
      } else {
        formik.setFieldValue('scheduleTemplate', 'day_shift');
      }
    }

    // Update preview
    updatePreview();
  };

  const updatePreview = () => {
    if (formik.values.startTime && formik.values.endTime) {
      const mockUser = {
        ...currentSelectedUser,
        workSchedules: [{
          type: formik.values.type,
          startTime: formik.values.startTime,
          endTime: formik.values.endTime,
          scheduleTemplate: formik.values.scheduleTemplate,
          effectiveFrom: formik.values.effectiveFrom,
          effectiveTo: formik.values.effectiveTo,
          priority: WorkScheduleUtils.getSchedulePriority(formik.values.type)
        }]
      };

      const effectiveSchedule = WorkScheduleUtils.getEffectiveSchedule(
        mockUser, 
        formik.values.specificDate || formik.values.effectiveFrom
      );

      setPreviewSchedule(effectiveSchedule);
    }
  };

  const handleDayOfWeekChange = (day) => {
    const currentDays = formik.values.daysOfWeek || [];
    const newDays = currentDays.includes(day) ? currentDays.filter(d => d !== day) : [...currentDays, day];
    
    formik.setFieldValue('daysOfWeek', newDays);
  };

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Typography variant="h6">
          {editingSchedule ? 'Edit' : 'Create'} Advanced Work Schedule
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {editingSchedule ? 'Update existing schedule' : 'Create specific schedules with priority-based resolution'}
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              {/* User Selection */}
              <Grid item xs={12} md={6}>
                <SelectField
                  label="Select User"
                  name="selectedUserId"
                  value={formik.values.selectedUserId}
                  onChange={(e) => handleWorkScheduleChange('selectedUserId', e.target.value)}
                  required
                >
                  {users.map((user) => (
                    <MenuItem key={user._id} value={user._id}>
                      {user.name} - {user.designation?.name || user.role || 'No Role'}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              {/* Schedule Type */}
              <Grid item xs={12} md={6}>
                <SelectField
                  label="Schedule Type"
                  name="type"
                  value={formik.values.type}
                  onChange={(e) => handleWorkScheduleChange('type', e.target.value)}
                  required
                >
                  {SCHEDULE_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label} (Priority: {type.priority})
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              {/* Schedule Template */}
              <Grid item xs={12} md={6}>
                <SelectField
                  label="Schedule Template"
                  name="scheduleTemplate"
                  value={formik.values.scheduleTemplate}
                  onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}
                  required
                >
                  {SCHEDULE_TEMPLATES.map((template) => (
                    <MenuItem key={template.value} value={template.value}>
                      {template.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              {/* Minimum Hours */}
              <Grid item xs={12} md={6}>
                <Input
                  label="Minimum Hours"
                  name="minimumHours"
                  type="number"
                  step="0.1"
                  value={formik.values.minimumHours}
                  onChange={(e) => handleWorkScheduleChange('minimumHours', e.target.value)}
                  required
                  helperText={
                    formik.values.startTime && formik.values.endTime ? `Calculated: ${WorkScheduleUtils.calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Enter start and end time to auto-calculate'
                  }
                />
              </Grid>

              {/* Time Range */}
              <Grid item xs={12} md={6}>
                <SelectField
                  label="Start Time"
                  name="startTime"
                  value={formik.values.startTime}
                  onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}
                  required
                >
                  {TIME_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              <Grid item xs={12} md={6}>
                <SelectField
                  label="End Time"
                  name="endTime"
                  value={formik.values.endTime}
                  onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}
                  required
                >
                  {TIME_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              {/* Date Range */}
              <Grid item xs={12} md={6}>
                <Input
                  label="Effective From"
                  name="effectiveFrom"
                  type="date"
                  value={formik.values.effectiveFrom}
                  onChange={(e) => handleWorkScheduleChange('effectiveFrom', e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Input
                  label="Effective To"
                  name="effectiveTo"
                  type="date"
                  value={formik.values.effectiveTo}
                  onChange={(e) => handleWorkScheduleChange('effectiveTo', e.target.value)}
                  required
                />
              </Grid>

              {/* Specific Date (for daily/time-specific schedules) */}
              {(formik.values.type === 'daily' || formik.values.type === 'time_specific') && (
                <Grid item xs={12} md={6}>
                  <Input
                    label="Specific Date"
                    name="specificDate"
                    type="date"
                    value={formik.values.specificDate}
                    onChange={(e) => handleWorkScheduleChange('specificDate', e.target.value)}
                    required
                  />
                </Grid>
              )}

              {/* Days of Week (for weekly schedules) */}
              {formik.values.type === 'weekly' && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Days of Week
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {dayNames.map((day, index) => (
                      <FormControlLabel
                        key={index}
                        control={
                          <Checkbox
                            checked={formik.values.daysOfWeek?.includes(index) || false}
                            onChange={() => handleDayOfWeekChange(index)}
                          />
                        }
                        label={day}
                      />
                    ))}
                  </Box>
                </Grid>
              )}

              {/* Description */}
              <Grid item xs={12}>
                <Input
                  label="Description"
                  name="description"
                  multiline
                  rows={3}
                  value={formik.values.description}
                  onChange={(e) => handleWorkScheduleChange('description', e.target.value)}
                  placeholder="Enter description for this schedule..."
                />
              </Grid>

              {/* Preview */}
              {previewSchedule && (
                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="subtitle2">Schedule Preview:</Typography>
                    <Typography variant="body2">
                      {previewSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift'}: {' '}
                      {previewSchedule.startTime} - {previewSchedule.endTime} ({previewSchedule.minimumHours}h)
                    </Typography>
                  </Alert>
                </Grid>
              )}
            </Grid>
          </form>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancel
        </Button>
        <Button
          onClick={formik.handleSubmit}
          variant="contained"
          color="primary"
        >
          {editingSchedule ? 'Update Schedule' : 'Create Schedule'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

AdvancedWorkScheduleForm.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedUser: PropTypes.object,
  selectedDate: PropTypes.string,
  scheduleType: PropTypes.string,
  editingSchedule: PropTypes.object
};

export default AdvancedWorkScheduleForm;
