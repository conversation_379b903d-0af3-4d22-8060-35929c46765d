import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  MenuItem,
  Alert,
  Chip,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import Input from 'components/Input';
import SelectField from 'components/SelectField';
import { UserActions, GeneralActions } from 'slices/actions';
import { GeneralSelector, UserSelector } from 'selectors';
import { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';
import WorkScheduleUtils from 'utils/workScheduleUtils';
import dayjs from 'dayjs';

const OptimizedScheduleForm = ({ 
  open, 
  onClose, 
  selectedUser, 
  selectedDate, 
  scheduleType = 'daily',
  editingSchedule = null 
}) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));

  useEffect(() => {
    if (success) {
      toast.success(`Schedule ${editingSchedule ? 'updated' : 'created'} successfully!`, {
        position: "top-right",
        autoClose: 3000,
        closeOnClick: true,
      });
      onClose();
      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));
    }
  }, [success, onClose, dispatch, editingSchedule]);

  // Get proper date range based on schedule type
  const getDateRange = (type, date) => {
    const targetDate = dayjs(date);
    switch (type) {
      case 'time_specific':
      case 'daily':
        return {
          effectiveFrom: targetDate.format('YYYY-MM-DD'),
          effectiveTo: targetDate.format('YYYY-MM-DD'),
          specificDate: targetDate.format('YYYY-MM-DD')
        };
      case 'weekly':
        return {
          effectiveFrom: targetDate.startOf('week').format('YYYY-MM-DD'),
          effectiveTo: targetDate.endOf('week').format('YYYY-MM-DD'),
          specificDate: targetDate.format('YYYY-MM-DD')
        };
      case 'monthly':
        return {
          effectiveFrom: targetDate.startOf('month').format('YYYY-MM-DD'),
          effectiveTo: targetDate.endOf('month').format('YYYY-MM-DD'),
          specificDate: targetDate.format('YYYY-MM-DD')
        };
      default:
        return {
          effectiveFrom: targetDate.format('YYYY-MM-DD'),
          effectiveTo: targetDate.format('YYYY-MM-DD'),
          specificDate: targetDate.format('YYYY-MM-DD')
        };
    }
  };

  const initialDateRange = getDateRange(editingSchedule?.type || scheduleType, selectedDate);

  const formik = useFormik({
    initialValues: {
      selectedUserId: editingSchedule?.userId || selectedUser?._id || (users.length > 0 ? users[0]._id : ''),
      type: editingSchedule?.type || scheduleType,
      scheduleTemplate: editingSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate,
      startTime: editingSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime,
      endTime: editingSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime,
      minimumHours: editingSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours,
      effectiveFrom: editingSchedule?.effectiveFrom || initialDateRange.effectiveFrom,
      effectiveTo: editingSchedule?.effectiveTo || initialDateRange.effectiveTo,
      specificDate: editingSchedule?.specificDate || initialDateRange.specificDate,
      daysOfWeek: editingSchedule?.daysOfWeek || [1, 2, 3, 4, 5],
      description: editingSchedule?.description || ''
    },
    enableReinitialize: true,
    onSubmit: (values) => handleSubmit(values)
  });

  const handleSubmit = (values) => {
    const targetUser = users.find(u => u._id === values.selectedUserId);
    if (!targetUser) {
      toast.error('Please select a valid user');
      return;
    }

    let updatedWorkSchedules = [];

    if (editingSchedule) {
      // Update existing schedule
      updatedWorkSchedules = (targetUser.workSchedules || []).map((schedule) => {
        if (schedule.id === editingSchedule.id) {
          return {
            ...schedule,
            ...values,
            minimumHours: parseFloat(values.minimumHours),
            priority: WorkScheduleUtils.getSchedulePriority(values.type),
            specificDate: values.type === 'daily' || values.type === 'time_specific' ? values.specificDate : null,
            daysOfWeek: values.type === 'weekly' ? values.daysOfWeek : null
          };
        }
        return schedule;
      });
    } else {
      // Create new schedule
      const scheduleEntry = WorkScheduleUtils.createScheduleEntry(targetUser._id, {
        ...values,
        minimumHours: parseFloat(values.minimumHours),
        specificDate: values.type === 'daily' || values.type === 'time_specific' ? values.specificDate : null,
        daysOfWeek: values.type === 'weekly' ? values.daysOfWeek : null
      });
      updatedWorkSchedules = [...(targetUser.workSchedules || []), scheduleEntry];
    }

    dispatch(UserActions.updateUser({
      id: targetUser._id,
      workSchedules: updatedWorkSchedules
    }));
  };

  const handleFieldChange = (field, value) => {
    formik.setFieldValue(field, value);
    
    // Update date ranges when type changes
    if (field === 'type') {
      const newDateRange = getDateRange(value, formik.values.specificDate);
      formik.setFieldValue('effectiveFrom', newDateRange.effectiveFrom);
      formik.setFieldValue('effectiveTo', newDateRange.effectiveTo);
    }
    
    // Auto-calculate hours
    if (field === 'startTime' || field === 'endTime') {
      const startTime = field === 'startTime' ? value : formik.values.startTime;
      const endTime = field === 'endTime' ? value : formik.values.endTime;
      
      if (startTime && endTime) {
        const calculatedHours = WorkScheduleUtils.calculateHours(startTime, endTime);
        formik.setFieldValue('minimumHours', calculatedHours);
      }
    }
    
    // Auto-detect night shift
    if (field === 'startTime') {
      const hour = parseInt(value.split(':')[0], 10);
      if (hour >= 22 || hour < 6) {
        formik.setFieldValue('scheduleTemplate', 'night_shift');
      } else {
        formik.setFieldValue('scheduleTemplate', 'day_shift');
      }
    }
  };

  const handleDayOfWeekChange = (day) => {
    const currentDays = formik.values.daysOfWeek || [];
    const newDays = currentDays.includes(day) ? currentDays.filter(d => d !== day) : [...currentDays, day];
    formik.setFieldValue('daysOfWeek', newDays);
  };

  const scheduleTypes = [
    { value: 'time_specific', label: 'Time-Specific (Highest Priority)', priority: 4 },
    { value: 'daily', label: 'Daily Schedule', priority: 3 },
    { value: 'weekly', label: 'Weekly Schedule', priority: 2 },
    { value: 'monthly', label: 'Monthly Schedule', priority: 1 }
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">
          {editingSchedule ? 'Edit' : 'Create'} Work Schedule
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {scheduleType === 'time_specific' && 'For specific time slots (meetings, appointments)'}
          {scheduleType === 'daily' && 'For specific dates with different hours'}
          {scheduleType === 'weekly' && 'For recurring weekly patterns'}
          {scheduleType === 'monthly' && 'For monthly schedule patterns'}
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
          {/* User Selection */}
          <SelectField
            label="Select User"
            name="selectedUserId"
            value={formik.values.selectedUserId}
            onChange={(e) => handleFieldChange('selectedUserId', e.target.value)}
            required
          >
            {users.map((user) => (
              <MenuItem key={user._id} value={user._id}>
                {user.name} - {user.designation?.name || 'No Role'}
              </MenuItem>
            ))}
          </SelectField>

          {/* Schedule Type */}
          <SelectField
            label="Schedule Type"
            name="type"
            value={formik.values.type}
            onChange={(e) => handleFieldChange('type', e.target.value)}
            required
          >
            {scheduleTypes.map((type) => (
              <MenuItem key={type.value} value={type.value}>
                {type.label}
              </MenuItem>
            ))}
          </SelectField>

          {/* Date Selection */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Input
              label="Specific Date"
              name="specificDate"
              type="date"
              value={formik.values.specificDate}
              onChange={(e) => handleFieldChange('specificDate', e.target.value)}
              required
              sx={{ flex: 1 }}
            />
            <Input
              label="Effective From"
              name="effectiveFrom"
              type="date"
              value={formik.values.effectiveFrom}
              onChange={(e) => handleFieldChange('effectiveFrom', e.target.value)}
              required
              sx={{ flex: 1 }}
            />
            <Input
              label="Effective To"
              name="effectiveTo"
              type="date"
              value={formik.values.effectiveTo}
              onChange={(e) => handleFieldChange('effectiveTo', e.target.value)}
              required
              sx={{ flex: 1 }}
            />
          </Box>

          {/* Time Range */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <SelectField
              label="Start Time"
              name="startTime"
              value={formik.values.startTime}
              onChange={(e) => handleFieldChange('startTime', e.target.value)}
              required
              sx={{ flex: 1 }}
            >
              {TIME_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </SelectField>
            <SelectField
              label="End Time"
              name="endTime"
              value={formik.values.endTime}
              onChange={(e) => handleFieldChange('endTime', e.target.value)}
              required
              sx={{ flex: 1 }}
            >
              {TIME_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </SelectField>
          </Box>

          {/* Schedule Template and Hours */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <SelectField
              label="Schedule Template"
              name="scheduleTemplate"
              value={formik.values.scheduleTemplate}
              onChange={(e) => handleFieldChange('scheduleTemplate', e.target.value)}
              required
              sx={{ flex: 1 }}
            >
              {SCHEDULE_TEMPLATES.map((template) => (
                <MenuItem key={template.value} value={template.value}>
                  {template.label}
                </MenuItem>
              ))}
            </SelectField>
            <Input
              label="Minimum Hours"
              name="minimumHours"
              type="number"
              step="0.1"
              value={formik.values.minimumHours}
              onChange={(e) => handleFieldChange('minimumHours', e.target.value)}
              required
              helperText={
                formik.values.startTime && formik.values.endTime ? `Calculated: ${WorkScheduleUtils.calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Auto-calculated from time range'
              }
              sx={{ flex: 1 }}
            />
          </Box>

          {/* Days of Week (for weekly schedules) */}
          {formik.values.type === 'weekly' && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Days of Week:
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {dayNames.map((day, index) => (
                  <FormControlLabel
                    key={index}
                    control={
                      <Checkbox
                        checked={formik.values.daysOfWeek?.includes(index) || false}
                        onChange={() => handleDayOfWeekChange(index)}
                      />
                    }
                    label={day}
                  />
                ))}
              </Box>
            </Box>
          )}

          {/* Description */}
          <Input
            label="Description"
            name="description"
            multiline
            rows={2}
            value={formik.values.description}
            onChange={(e) => handleFieldChange('description', e.target.value)}
            placeholder="Enter description for this schedule..."
          />

          {/* Schedule Info */}
          <Alert severity="info">
            <Typography variant="body2">
              <strong>Schedule Type:</strong> {formik.values.type.replace('_', ' ').toUpperCase()} 
              <Chip 
                label={`Priority ${WorkScheduleUtils.getSchedulePriority(formik.values.type)}`} 
                size="small" 
                color="primary" 
                sx={{ ml: 1 }}
              />
            </Typography>
            <Typography variant="body2">
              <strong>Effective Period:</strong> {formik.values.effectiveFrom} to {formik.values.effectiveTo}
            </Typography>
            {formik.values.type === 'daily' && (
              <Typography variant="caption" color="text.secondary">
                This schedule will only apply on {formik.values.specificDate}
              </Typography>
            )}
            {formik.values.type === 'time_specific' && (
              <Typography variant="caption" color="text.secondary">
                This schedule will only apply on {formik.values.specificDate} from {formik.values.startTime} to {formik.values.endTime}
              </Typography>
            )}
          </Alert>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancel
        </Button>
        <Button 
          onClick={formik.handleSubmit} 
          variant="contained" 
          color="primary"
        >
          {editingSchedule ? 'Update' : 'Create'} Schedule
        </Button>
      </DialogActions>
    </Dialog>
  );
};

OptimizedScheduleForm.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedUser: PropTypes.object,
  selectedDate: PropTypes.string,
  scheduleType: PropTypes.string,
  editingSchedule: PropTypes.object
};

export default OptimizedScheduleForm;
