import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>,
  Alert,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { Schedule, AccessTime, CalendarToday, CheckCircle, Error } from '@mui/icons-material';
import WorkScheduleUtils from 'utils/workScheduleUtils';
import AttendanceValidator from 'components/AttendanceValidator';
import dayjs from 'dayjs';

const ScheduleDemo = () => {
  const [showValidator, setShowValidator] = useState(false);
  const [validationAction, setValidationAction] = useState('checkin');
  const [validationTime, setValidationTime] = useState(new Date());

  // Demo user with multiple schedules
  const demoUser = {
    _id: 'demo-user',
    name: '<PERSON>',
    designation: { name: 'Software Developer' },
    workSchedule: {
      scheduleTemplate: 'day_shift',
      startTime: '09:00',
      endTime: '17:30',
      minimumHours: 8.5
    },
    workSchedules: [
      {
        id: 'weekly-1',
        type: 'weekly',
        priority: 2,
        scheduleTemplate: 'day_shift',
        startTime: '08:00',
        endTime: '16:00',
        minimumHours: 8,
        effectiveFrom: '2025-01-13',
        effectiveTo: '2025-01-17',
        daysOfWeek: [1, 2, 3, 4, 5], // Mon-Fri
        description: 'Project Week - Early Hours'
      },
      {
        id: 'daily-1',
        type: 'daily',
        priority: 3,
        scheduleTemplate: 'day_shift',
        startTime: '10:00',
        endTime: '18:00',
        minimumHours: 8,
        effectiveFrom: '2025-01-15',
        effectiveTo: '2025-01-15',
        specificDate: '2025-01-15',
        description: 'Client Meeting Day - Late Start'
      },
      {
        id: 'time-1',
        type: 'time_specific',
        priority: 4,
        scheduleTemplate: 'day_shift',
        startTime: '14:00',
        endTime: '16:00',
        minimumHours: 2,
        effectiveFrom: '2025-01-15',
        effectiveTo: '2025-01-15',
        specificDate: '2025-01-15',
        description: 'Important Client Meeting'
      }
    ]
  };

  const testDates = [
    { date: '2025-01-12', label: 'Sunday (Default Schedule)' },
    { date: '2025-01-14', label: 'Tuesday (Weekly Schedule)' },
    { date: '2025-01-15', label: 'Wednesday (Daily + Time Override)' },
    { date: '2025-01-16', label: 'Thursday (Weekly Schedule)' }
  ];

  const testTimes = [
    { time: '08:30', action: 'checkin' },
    { time: '14:30', action: 'checkin' },
    { time: '16:30', action: 'checkout' },
    { time: '18:30', action: 'checkout' }
  ];

  const getEffectiveScheduleForDate = (date, time = null) => {
    return WorkScheduleUtils.getEffectiveSchedule(demoUser, date, time);
  };

  const handleTestValidation = (time, action) => {
    const testDateTime = dayjs().hour(parseInt(time.split(':')[0])).minute(parseInt(time.split(':')[1]));
    setValidationTime(testDateTime.toDate());
    setValidationAction(action);
    setShowValidator(true);
  };

  const getScheduleTypeIcon = (type) => {
    switch (type) {
      case 'time_specific': return <AccessTime color="error" />;
      case 'daily': return <CalendarToday color="warning" />;
      case 'weekly': return <Schedule color="info" />;
      default: return <Schedule color="action" />;
    }
  };

  const getScheduleTypeColor = (type) => {
    switch (type) {
      case 'time_specific': return 'error';
      case 'daily': return 'warning';
      case 'weekly': return 'info';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Work Schedule System Demo
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        This demo shows how multiple work schedules work with priority-based resolution and attendance validation.
      </Alert>

      {/* User Info */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Demo User: {demoUser.name}
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {demoUser.designation.name}
          </Typography>
          
          <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
            Default Schedule:
          </Typography>
          <Chip 
            label={`${demoUser.workSchedule.scheduleTemplate.replace('_', ' ').toUpperCase()}: ${demoUser.workSchedule.startTime}-${demoUser.workSchedule.endTime}`}
            color="default"
          />
          
          <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
            Specific Schedules ({demoUser.workSchedules.length}):
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {demoUser.workSchedules.map((schedule) => (
              <Chip
                key={schedule.id}
                icon={getScheduleTypeIcon(schedule.type)}
                label={`${schedule.type.replace('_', ' ').toUpperCase()}: ${schedule.startTime}-${schedule.endTime}`}
                color={getScheduleTypeColor(schedule.type)}
                size="small"
              />
            ))}
          </Box>
        </CardContent>
      </Card>

      {/* Schedule Resolution Demo */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Schedule Resolution by Date
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            See which schedule is active for different dates based on priority.
          </Typography>
          
          <List>
            {testDates.map((test, index) => {
              const effectiveSchedule = getEffectiveScheduleForDate(test.date);
              const scheduleType = effectiveSchedule.type || 'default';
              
              return (
                <ListItem key={index} divider>
                  <ListItemIcon>
                    {getScheduleTypeIcon(scheduleType)}
                  </ListItemIcon>
                  <ListItemText
                    primary={test.label}
                    secondary={
                      <Box>
                        <Typography variant="body2">
                          <strong>Active Schedule:</strong> {scheduleType.replace('_', ' ').toUpperCase()}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Hours:</strong> {effectiveSchedule.startTime} - {effectiveSchedule.endTime} ({effectiveSchedule.minimumHours}h)
                        </Typography>
                        {effectiveSchedule.description && (
                          <Typography variant="caption" color="text.secondary">
                            {effectiveSchedule.description}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  <Chip 
                    label={`Priority ${effectiveSchedule.priority || 1}`}
                    color={getScheduleTypeColor(scheduleType)}
                    size="small"
                  />
                </ListItem>
              );
            })}
          </List>
        </CardContent>
      </Card>

      {/* Attendance Validation Demo */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Attendance Validation Demo
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Test check-in/check-out validation against active schedules.
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2 }}>
            {testTimes.map((test, index) => {
              const validation = WorkScheduleUtils.validateCheckInOut(
                demoUser, 
                dayjs().hour(parseInt(test.time.split(':')[0])).minute(parseInt(test.time.split(':')[1])).toDate(),
                test.action
              );
              
              return (
                <Button
                  key={index}
                  variant="outlined"
                  startIcon={validation.isValid ? <CheckCircle color="success" /> : <Error color="error" />}
                  color={validation.isValid ? "success" : "error"}
                  onClick={() => handleTestValidation(test.time, test.action)}
                  sx={{ minWidth: 200 }}
                >
                  {test.action === 'checkin' ? 'Check-in' : 'Check-out'} at {test.time}
                </Button>
              );
            })}
          </Box>
        </CardContent>
      </Card>

      {/* Priority Explanation */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            How Priority Resolution Works
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon><AccessTime color="error" /></ListItemIcon>
              <ListItemText
                primary="Time-Specific Schedule (Priority 4)"
                secondary="Overrides all other schedules for exact time slots (e.g., meetings, appointments)"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><CalendarToday color="warning" /></ListItemIcon>
              <ListItemText
                primary="Daily Schedule (Priority 3)"
                secondary="Overrides weekly and default schedules for specific dates"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Schedule color="info" /></ListItemIcon>
              <ListItemText
                primary="Weekly Schedule (Priority 2)"
                secondary="Overrides default schedule for specified days of the week"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Schedule color="action" /></ListItemIcon>
              <ListItemText
                primary="Default Schedule (Priority 1)"
                secondary="Fallback schedule used when no specific schedule applies"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* Attendance Validator */}
      <AttendanceValidator
        open={showValidator}
        onClose={() => setShowValidator(false)}
        onConfirm={(validation) => {
          console.log('Validation result:', validation);
        }}
        user={demoUser}
        action={validationAction}
        timestamp={validationTime}
      />
    </Box>
  );
};

export default ScheduleDemo;
