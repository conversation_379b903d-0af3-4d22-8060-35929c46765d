import React, { useState } from 'react';
import {
  Box, Typography, Grid, IconButton, Dialog, DialogTitle, DialogContent,
  TextField, DialogActions, Button
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

const days = ['Mon 09', 'Tue 10', 'Wed 11', 'Thu 12', 'Fri 13', 'Sat 14', 'Sun 15'];

const users = [
  { name: '<PERSON><PERSON><PERSON>', role: 'System Admin' },
  { name: '<PERSON><PERSON>', role: 'Manager' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Manager' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Manager' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Normal User' },
  { name: 'Nachiket Tandel', role: 'Normal User' },
  { name: '<PERSON><PERSON>', role: 'Normal User' },
  { name: '<PERSON><PERSON>', role: 'Normal User' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Normal User' },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', role: 'Normal User' },
];

const WeekWorkSchedule = () => {
  const [open, setOpen] = useState(false);
  const [selectedCell, setSelectedCell] = useState(null);

  const handleCellClick = (user, day) => {
    setSelectedCell({ user, day });
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedCell(null);
  };

  return (
    <Box p={2}>
      <Typography variant="h5" mb={2}>Work Schedule</Typography>
      <Grid container spacing={1}>
        <Grid item xs={2.5}></Grid>
        {days.map((day) => (
          <Grid key={day} item xs>
            <Typography variant="subtitle2" align="center">{day}</Typography>
          </Grid>
        ))}
      </Grid>

      {users.map((user, rowIndex) => (
        <Grid container key={rowIndex} spacing={1} alignItems="center" mt={1}>
          <Grid item xs={2.5}>
            <Box>
              <Typography variant="body1" fontWeight="bold">{user.name}</Typography>
              <Typography variant="caption">{user.role}</Typography>
            </Box>
          </Grid>
          {days.map((day, colIndex) => (
            <Grid item xs key={colIndex}>
              <Box
                sx={{
                  height: 60,
                  bgcolor: 'rgba(0,0,0,0.03)',
                  borderRadius: 1,
                  position: 'relative',
                  backdropFilter: 'blur(3px)',
                  '&:hover .add-icon': { opacity: 1 },
                }}
              >
                <IconButton
                  className="add-icon"
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    opacity: 0,
                    transition: 'opacity 0.3s',
                  }}
                  onClick={() => handleCellClick(user.name, day)}
                >
                  <AddCircleOutlineIcon fontSize="small" />
                </IconButton>
              </Box>
            </Grid>
          ))}
        </Grid>
      ))}

      {/* Modal for adding schedule */}
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Add Work Schedule</DialogTitle>
        <DialogContent>
          <Typography mb={2}>
            {selectedCell?.user} - {selectedCell?.day}
          </Typography>
          <TextField fullWidth label="Schedule Detail" />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button variant="contained" onClick={handleClose}>Save</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WeekWorkSchedule;
