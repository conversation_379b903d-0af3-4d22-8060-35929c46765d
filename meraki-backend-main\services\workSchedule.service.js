'use strict';

/**
 * Work Schedule Service
 *
 * This service provides methods for work schedule validation and management.
 */

/**
 * Validate work schedule data
 * @param {Object} workSchedule - Work schedule object to validate
 * @returns {Object} Validation result with isValid flag and errors array
 */
exports.validateWorkSchedule = (workSchedule) => {
    const errors = [];
    
    if (!workSchedule) {
        errors.push('Work schedule data is required');
        return { isValid: false, errors };
    }

    // Validate schedule template
    if (!workSchedule.scheduleTemplate || !['day_shift', 'night_shift'].includes(workSchedule.scheduleTemplate)) {
        errors.push('Valid schedule template is required (day_shift or night_shift)');
    }

    // Validate shift dates
    if (!workSchedule.shiftStart) {
        errors.push('Shift start date is required');
    }

    if (!workSchedule.shiftEnd) {
        errors.push('Shift end date is required');
    }

    if (workSchedule.shiftStart && workSchedule.shiftEnd) {
        const startDate = new Date(workSchedule.shiftStart);
        const endDate = new Date(workSchedule.shiftEnd);
        
        if (endDate < startDate) {
            errors.push('Shift end date must be after or equal to shift start date');
        }
    }

    // Validate times
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    
    if (!workSchedule.startTime || !timeRegex.test(workSchedule.startTime)) {
        errors.push('Valid start time is required (HH:MM format)');
    }

    if (!workSchedule.endTime || !timeRegex.test(workSchedule.endTime)) {
        errors.push('Valid end time is required (HH:MM format)');
    }

    // Validate minimum hours
    if (workSchedule.minimumHours === undefined || workSchedule.minimumHours === null) {
        errors.push('Minimum hours is required');
    } else if (isNaN(workSchedule.minimumHours) || workSchedule.minimumHours < 0 || workSchedule.minimumHours > 24) {
        errors.push('Minimum hours must be a number between 0 and 24');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Get default work schedule templates
 * @returns {Array} Array of default work schedule templates
 */
exports.getWorkScheduleTemplates = async () => {
    return [
        {
            id: 'day_shift',
            name: 'Day Shift',
            startTime: '09:00',
            endTime: '17:00',
            minimumHours: 8.0
        },
        {
            id: 'night_shift',
            name: 'Night Shift',
            startTime: '22:00',
            endTime: '06:00',
            minimumHours: 8.0
        }
    ];
};

/**
 * Validate work schedule template data
 * @param {Object} template - Template object to validate
 * @returns {Object} Validation result with isValid flag and errors array
 */
exports.validateWorkScheduleTemplate = (template) => {
    const errors = [];
    
    if (!template) {
        errors.push('Template data is required');
        return { isValid: false, errors };
    }

    if (!template.name || template.name.trim().length === 0) {
        errors.push('Template name is required');
    }

    // Use the same validation as work schedule
    return this.validateWorkSchedule(template);
};

/**
 * Create work schedule template (placeholder for future implementation)
 * @param {Object} templateData - Template data
 * @returns {Object} Created template
 */
exports.createWorkScheduleTemplate = async (templateData) => {
    // This is a placeholder - in a real implementation, you would save to database
    return {
        id: Date.now().toString(),
        ...templateData,
        createdAt: new Date()
    };
};

/**
 * Update work schedule template (placeholder for future implementation)
 * @param {String} templateId - Template ID
 * @param {Object} templateData - Updated template data
 * @returns {Object} Update result
 */
exports.updateWorkScheduleTemplate = async (templateId, templateData) => {
    // This is a placeholder - in a real implementation, you would update in database
    return { success: true, templateId, updatedAt: new Date() };
};

/**
 * Delete work schedule template (placeholder for future implementation)
 * @param {String} templateId - Template ID
 * @returns {Object} Delete result
 */
exports.deleteWorkScheduleTemplate = async (templateId) => {
    // This is a placeholder - in a real implementation, you would delete from database
    return { success: true, templateId, deletedAt: new Date() };
};
