import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  MenuItem,
  Box
} from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import Input from 'components/Input';
import SelectField from 'components/SelectField';
import { UserActions } from 'slices/actions';
import { GeneralSelector } from 'selectors';
import { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';

const DayWorkScheduleForm = ({ open, onClose, selectedUser, selectedDate, selectedHour }) => {
  const dispatch = useDispatch();
  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));

  useEffect(() => {
    if (success) {
      toast.success(`Work schedule updated successfully!`, {
        position: "top-right",
        autoClose: 3000,
        closeOnClick: true,
      });
      onClose();
    }
  }, [success, onClose]);

  // Helper function to format date for input field
  const formatDateForInput = (dateValue) => {
    if (!dateValue) {
      return new Date().toISOString().split('T')[0];
    }
    if (typeof dateValue === 'string') {
      return dateValue.split('T')[0];
    }
    return new Date(dateValue).toISOString().split('T')[0];
  };

  const formik = useFormik({
    initialValues: {
      scheduleTemplate: selectedUser?.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate,
      shiftStart: formatDateForInput(selectedDate || selectedUser?.workSchedule?.shiftStart),
      shiftEnd: formatDateForInput(selectedDate || selectedUser?.workSchedule?.shiftEnd),
      startTime: selectedUser?.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime,
      endTime: selectedUser?.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime,
      minimumHours: selectedUser?.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours,
      workDescription: ''
    },
    enableReinitialize: true,
    onSubmit: (values) => {
      handleSubmit(values);
    }
  });

  const handleSubmit = (values) => {
    if (!selectedUser?._id) {
      toast.error('User information is missing');
      return;
    }

    const params = {
      id: selectedUser._id,
      workSchedule: {
        scheduleTemplate: values.scheduleTemplate,
        shiftStart: values.shiftStart,
        shiftEnd: values.shiftEnd,
        startTime: values.startTime,
        endTime: values.endTime,
        minimumHours: parseFloat(values.minimumHours)
      }
    };

    dispatch(UserActions.updateUser(params));
  };

  // Helper function to calculate hours between two times
  const calculateHours = (startTime, endTime) => {
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);

    let startMinutes = (startHour * 60) + startMin;
    let endMinutes = (endHour * 60) + endMin;

    // Handle overnight shifts (night shift)
    if (endMinutes <= startMinutes) {
      endMinutes += (24 * 60); // Add 24 hours
    }

    const diffMinutes = endMinutes - startMinutes;
    return (diffMinutes / 60).toFixed(2);
  };

  const handleWorkScheduleChange = (field, value) => {
    formik.setFieldValue(field, value);

    // Auto-calculate hours when start or end time changes
    if (field === 'startTime' || field === 'endTime') {
      const startTime = field === 'startTime' ? value : formik.values.startTime;
      const endTime = field === 'endTime' ? value : formik.values.endTime;

      if (startTime && endTime) {
        const calculatedHours = calculateHours(startTime, endTime);
        formik.setFieldValue('minimumHours', calculatedHours);
      }
    }

    // Auto-suggest schedule template based on time
    if (field === 'startTime') {
      const hour = parseInt(value.split(':')[0], 10);
      if (hour >= 22 || hour < 6) {
        formik.setFieldValue('scheduleTemplate', 'night_shift');
      } else {
        formik.setFieldValue('scheduleTemplate', 'day_shift');
      }
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">
          Day Work Schedule - {selectedUser?.name}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Date: {selectedDate} {selectedHour && `| Time: ${selectedHour}`}
        </Typography>
        {selectedUser?.workSchedule && (
          <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 1 }}>
            Current: {selectedUser.workSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift'}
            ({selectedUser.workSchedule.startTime}-{selectedUser.workSchedule.endTime},
            {selectedUser.workSchedule.minimumHours}h min)
          </Typography>
        )}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <SelectField
                  label="Schedule Template"
                  name="scheduleTemplate"
                  value={formik.values.scheduleTemplate}
                  onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}
                  required
                >
                  {SCHEDULE_TEMPLATES.map((template) => (
                    <MenuItem key={template.value} value={template.value}>
                      {template.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Input
                  label="Minimum Hours"
                  name="minimumHours"
                  type="number"
                  step="0.1"
                  value={formik.values.minimumHours}
                  onChange={(e) => handleWorkScheduleChange('minimumHours', e.target.value)}
                  required
                  helperText={
                    formik.values.startTime && formik.values.endTime ? `Calculated: ${calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Enter start and end time to auto-calculate'
                  }
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Input
                  label="Shift Start Date"
                  name="shiftStart"
                  type="date"
                  value={formik.values.shiftStart}
                  onChange={(e) => handleWorkScheduleChange('shiftStart', e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Input
                  label="Shift End Date"
                  name="shiftEnd"
                  type="date"
                  value={formik.values.shiftEnd}
                  onChange={(e) => handleWorkScheduleChange('shiftEnd', e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <SelectField
                  label="Start Time"
                  name="startTime"
                  value={formik.values.startTime}
                  onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}
                  required
                >
                  {TIME_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              <Grid item xs={12} sm={6}>
                <SelectField
                  label="End Time"
                  name="endTime"
                  value={formik.values.endTime}
                  onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}
                  required
                >
                  {TIME_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </SelectField>
              </Grid>

              <Grid item xs={12}>
                <Input
                  label="Work Description (Optional)"
                  name="workDescription"
                  multiline
                  rows={3}
                  value={formik.values.workDescription}
                  onChange={(e) => handleWorkScheduleChange('workDescription', e.target.value)}
                  placeholder="Enter any specific work description or notes for this schedule..."
                />
              </Grid>
            </Grid>
          </form>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancel
        </Button>
        <Button 
          onClick={formik.handleSubmit} 
          variant="contained" 
          color="primary"
        >
          Save Schedule
        </Button>
      </DialogActions>
    </Dialog>
  );
};

DayWorkScheduleForm.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedUser: PropTypes.object,
  selectedDate: PropTypes.string,
  selectedHour: PropTypes.string
};

export default DayWorkScheduleForm;
