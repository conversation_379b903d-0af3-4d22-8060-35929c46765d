import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  Chip,
  Divider
} from '@mui/material';
import { CheckCircle, Warning, Error, Schedule } from '@mui/icons-material';
import PropTypes from 'prop-types';
import WorkScheduleUtils from 'utils/workScheduleUtils';
import dayjs from 'dayjs';

const AttendanceValidator = ({ 
  open, 
  onClose, 
  onConfirm, 
  user, 
  action = 'checkin', 
  timestamp = new Date() 
}) => {
  const [validation, setValidation] = useState(null);

  React.useEffect(() => {
    if (open && user) {
      const validationResult = WorkScheduleUtils.validateCheckInOut(user, timestamp, action);
      setValidation(validationResult);
    }
  }, [open, user, timestamp, action]);

  const getStatusIcon = () => {
    if (!validation) return <Schedule />;
    
    if (validation.isValid) {
      return <CheckCircle color="success" />;
    } else {
      return <Error color="error" />;
    }
  };

  const getStatusColor = () => {
    if (!validation) return 'info';
    return validation.isValid ? 'success' : 'error';
  };

  const formatTime = (timestamp) => {
    return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss');
  };

  const getScheduleTypeLabel = (type) => {
    const labels = {
      'default': 'Default Schedule',
      'weekly': 'Weekly Schedule',
      'daily': 'Daily Schedule', 
      'time_specific': 'Time-Specific Schedule'
    };
    return labels[type] || 'Unknown Schedule';
  };

  if (!validation) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getStatusIcon()}
          <Typography variant="h6">
            {action === 'checkin' ? 'Check-In' : 'Check-Out'} Validation
          </Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            {user.name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {formatTime(timestamp)}
          </Typography>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Validation Result */}
        <Alert severity={getStatusColor()} sx={{ mb: 2 }}>
          <Typography variant="body1" fontWeight="bold">
            {validation.message}
          </Typography>
        </Alert>

        {/* Schedule Information */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Active Schedule:
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
            <Chip 
              label={getScheduleTypeLabel(validation.scheduleType)}
              color="primary"
              size="small"
            />
            <Chip 
              label={validation.schedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift'}
              color="secondary"
              size="small"
            />
          </Box>

          <Typography variant="body2">
            <strong>Working Hours:</strong> {validation.allowedRange}
          </Typography>
          
          <Typography variant="body2">
            <strong>Minimum Hours:</strong> {validation.schedule.minimumHours}h
          </Typography>

          {validation.schedule.description && (
            <Typography variant="body2">
              <strong>Description:</strong> {validation.schedule.description}
            </Typography>
          )}
        </Box>

        {/* Additional Information */}
        {action === 'checkin' && (
          <Box sx={{ p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
            <Typography variant="caption" color="info.dark">
              <strong>Check-in Policy:</strong> You can check-in up to 15 minutes before your scheduled start time.
            </Typography>
          </Box>
        )}

        {action === 'checkout' && (
          <Box sx={{ p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
            <Typography variant="caption" color="info.dark">
              <strong>Check-out Policy:</strong> You can check-out up to 30 minutes after your scheduled end time. 
              Late check-outs may require overtime approval.
            </Typography>
          </Box>
        )}

        {/* Schedule Priority Explanation */}
        {validation.scheduleType !== 'default' && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
            <Typography variant="caption" color="warning.dark">
              <strong>Note:</strong> This {getScheduleTypeLabel(validation.scheduleType).toLowerCase()} 
              overrides your default schedule for this time period.
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancel
        </Button>
        <Button 
          onClick={() => {
            onConfirm(validation);
            onClose();
          }}
          variant="contained" 
          color={validation.isValid ? "primary" : "warning"}
        >
          {validation.isValid ? 
            `Confirm ${action === 'checkin' ? 'Check-In' : 'Check-Out'}` : 
            'Force Override'
          }
        </Button>
      </DialogActions>
    </Dialog>
  );
};

AttendanceValidator.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  user: PropTypes.object.isRequired,
  action: PropTypes.oneOf(['checkin', 'checkout']),
  timestamp: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)])
};

export default AttendanceValidator;
