export const SCHEDULE_TEMPLATES = [
    { value: 'day_shift', label: 'Day Shift' },
    { value: 'night_shift', label: 'Night Shift' }
];

export const DEFAULT_WORK_SCHEDULE = {
    scheduleTemplate: 'day_shift',
    shiftStart: new Date().toISOString().split('T')[0], // Current date
    shiftEnd: new Date().toISOString().split('T')[0],   // Current date
    startTime: '09:00',
    endTime: '17:30',
    minimumHours: 8.30
};

export const TIME_OPTIONS = [];
for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        TIME_OPTIONS.push({
            value: timeString,
            label: timeString
        });
    }
}
