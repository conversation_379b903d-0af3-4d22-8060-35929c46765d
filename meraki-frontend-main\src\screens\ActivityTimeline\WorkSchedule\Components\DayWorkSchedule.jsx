import React, { useState } from 'react';
import {
  Avatar,
  Box,
  Typography,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

const users = [
  { name: '<PERSON><PERSON><PERSON>', role: 'System Admin' },
  { name: '<PERSON><PERSON>', role: 'Manager' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Manager' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Manager' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Normal User' },
  { name: '<PERSON><PERSON><PERSON> Tandel', role: 'Normal User' },
  { name: '<PERSON><PERSON>', role: 'Normal User' },
  { name: '<PERSON><PERSON> boreker', role: 'Normal User' },
  { name: '<PERSON><PERSON><PERSON>', role: 'Normal User' },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', role: 'Normal User' },
];

const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);

const SLOT_WIDTH = 60;
const USER_WIDTH = 200;
const ROW_HEIGHT = 60;

const DayWorkSchedule = () => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState(null);

  const handleClick = (user, hour) => {
    setSelected({ user, hour });
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelected(null);
  };

  return (
    <Box p={3}>
      <Typography variant="h5" mb={2}>Day Work Schedule</Typography>

      {/* Scrollable table wrapper */}
      <Box
        sx={{
          width: '100%',
          overflowX: 'auto',
          border: '1px solid #ccc',
          borderRadius: 1,
          whiteSpace: 'nowrap',
          '&::-webkit-scrollbar': {
            height: 8
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#999',
            borderRadius: 4
          }
        }}
      >
       <Box sx={{ minWidth: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px` }}>


          {/* Header */}
          <Box sx={{ display: 'flex', position: 'sticky', top: 0, zIndex: 2 }}>
            <Box
              sx={{
                width: USER_WIDTH,
                height: ROW_HEIGHT,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 13,
                borderRight: '1px solid #ccc',
                borderBottom: '1px solid #ccc',
                backgroundColor: '#f0f0f0',
                position: 'sticky',
                left: 0,
                zIndex: 3
              }}
            >
              User
            </Box>
            {hours.map((hour, idx) => (
              <Box
                key={idx}
                sx={{
                  width: SLOT_WIDTH,
                  height: ROW_HEIGHT,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: 12,
                  fontWeight: 600,
                  borderRight: '1px solid #ccc',
                  borderBottom: '1px solid #ccc',
                  backgroundColor: '#f0f0f0'
                }}
              >
                {hour}
              </Box>
            ))}
          </Box>

          {/* User Rows */}
          <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
            {users.map((user, uIdx) => (
              <Box key={uIdx} sx={{ display: 'flex' }}>
                {/* User Info */}
                <Box
                  sx={{
                    width: USER_WIDTH,
                    minHeight: ROW_HEIGHT,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    px: 2,
                    backgroundColor: '#fff',
                    borderRight: '1px solid #eee',
                    borderBottom: '1px solid #eee',
                    position: 'sticky',
                    left: 0,
                    zIndex: 1
                  }}
                >
                  <Avatar sx={{ width: 32, height: 32 }}>{user.name[0]}</Avatar>
                  <Box>
                    <Typography fontWeight={600} fontSize={13}>{user.name}</Typography>
                    <Typography variant="caption" color="text.secondary">{user.role}</Typography>
                  </Box>
                </Box>

                {/* Time Slots */}
                {hours.map((hour, hIdx) => (
                  <Box
                    key={hIdx}
                    sx={{
                      width: SLOT_WIDTH,
                      height: ROW_HEIGHT,
                      borderRight: '1px solid #eee',
                      borderBottom: '1px solid #eee',
                      position: 'relative',
                      backgroundColor: '#fafafa',
                      '&:hover .add-icon': { opacity: 1 }
                    }}
                  >
                    <Tooltip title="Add schedule">
                      <IconButton
                        className="add-icon"
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          opacity: 0,
                          transition: 'opacity 0.3s'
                        }}
                        onClick={() => handleClick(user.name, hour)}
                      >
                        <AddCircleOutlineIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                ))}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>

      {/* Dialog Box */}
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Add Schedule</DialogTitle>
        <DialogContent>
          <Typography mb={2}>{selected?.user} - {selected?.hour}</Typography>
          <TextField fullWidth label="Work Description" variant="outlined" />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button variant="contained" onClick={handleClose}>Save</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DayWorkSchedule;
