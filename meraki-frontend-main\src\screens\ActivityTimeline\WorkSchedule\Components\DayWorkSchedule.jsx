import React, { useState, useEffect } from 'react';
import {
  Avatar,
  Box,
  Typography,
  IconButton,
  Tooltip,
  Button
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import SettingsIcon from '@mui/icons-material/Settings';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { UserActions } from 'slices/actions';
import { UserSelector } from 'selectors';
import DayWorkScheduleForm from './DayWorkScheduleForm';
import ScheduleManager from './ScheduleManager';

const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);

const SLOT_WIDTH = 60;
const USER_WIDTH = 200;
const ROW_HEIGHT = 60;

const DayWorkSchedule = ({ dateRange }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState(null);
  const [showScheduleManager, setShowScheduleManager] = useState(false);
  const [managerUser, setManagerUser] = useState(null);

  // Get the current date from dateRange or default to today
  const currentDate = dateRange?.startDate ? dayjs(dateRange.startDate) : dayjs();
  const isDateRange = dateRange?.startDate !== dateRange?.endDate;

  // Fetch users when component mounts
  useEffect(() => {
    dispatch(UserActions.getUsers());
  }, [dispatch]);

  const handleClick = (user, hour) => {
    setSelected({
      user,
      hour,
      date: currentDate.format('YYYY-MM-DD')
    });
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelected(null);
  };

  const handleOpenScheduleManager = (user) => {
    setManagerUser(user);
    setShowScheduleManager(true);
  };

  const handleCloseScheduleManager = () => {
    setShowScheduleManager(false);
    setManagerUser(null);
  };

  // Format the date display
  const getDateDisplayText = () => {
    if (isDateRange && dateRange?.endDate) {
      const endDate = dayjs(dateRange.endDate);
      if (currentDate.month() === endDate.month() && currentDate.year() === endDate.year()) {
        return `${currentDate.format("MMM D")} - ${endDate.format("D, YYYY")}`;
      } else if (currentDate.year() === endDate.year()) {
        return `${currentDate.format("MMM D")} - ${endDate.format("MMM D, YYYY")}`;
      } else {
        return `${currentDate.format("MMM D, YYYY")} - ${endDate.format("MMM D, YYYY")}`;
      }
    } else {
      return currentDate.format("dddd, MMMM D, YYYY");
    }
  };

  return (
    <Box p={3}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5">
          Day Work Schedule - {getDateDisplayText()}
        </Typography>
        <Button
          variant="outlined"
          size="small"
          startIcon={<SettingsIcon />}
          onClick={() => users.length > 0 && handleOpenScheduleManager(users[0])}
          disabled={users.length === 0}
        >
          Manage Schedules
        </Button>
      </Box>

      {/* Scrollable table wrapper */}
      <Box
        sx={{
          width: '100%',
          overflowX: 'auto',
          border: '1px solid #ccc',
          borderRadius: 1,
          whiteSpace: 'nowrap',
          '&::-webkit-scrollbar': {
            height: 8
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#999',
            borderRadius: 4
          }
        }}
      >
       <Box sx={{ minWidth: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px` }}>

          {/* Header */}
          <Box sx={{ display: 'flex', position: 'sticky', top: 0, zIndex: 2 }}>
            <Box
              sx={{
                width: USER_WIDTH,
                height: ROW_HEIGHT,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 13,
                borderRight: '1px solid #ccc',
                borderBottom: '1px solid #ccc',
                backgroundColor: '#f0f0f0',
                position: 'sticky',
                left: 0,
                zIndex: 3
              }}
            >
              User
            </Box>
            {hours.map((hour, idx) => (
              <Box
                key={idx}
                sx={{
                  width: SLOT_WIDTH,
                  height: ROW_HEIGHT,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: 12,
                  fontWeight: 600,
                  borderRight: '1px solid #ccc',
                  borderBottom: '1px solid #ccc',
                  backgroundColor: '#f0f0f0'
                }}
              >
                {hour}
              </Box>
            ))}
          </Box>

          {/* User Rows */}
          <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
            {users.map((user, uIdx) => (
              <Box key={uIdx} sx={{ display: 'flex' }}>
                {/* User Info */}
                <Box
                  sx={{
                    width: USER_WIDTH,
                    minHeight: ROW_HEIGHT,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    px: 2,
                    backgroundColor: '#fff',
                    borderRight: '1px solid #eee',
                    borderBottom: '1px solid #eee',
                    position: 'sticky',
                    left: 0,
                    zIndex: 1
                  }}
                > 
                  <Avatar sx={{ width: 32, height: 32 }}>
                    {user.name ? user.name[0].toUpperCase() : 'U'}
                  </Avatar>
                  <Box>
                    <Typography fontWeight={600} fontSize={13}>{user.name || 'Unknown User'}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {user.designation?.name || user.role || 'No Role'}
                    </Typography>
                    {/* Show work schedule indicator if different from default */}
                    {user.workSchedule && (
                      user.workSchedule.startTime !== '09:00' ||
                      user.workSchedule.endTime !== '17:30' ||
                      user.workSchedule.scheduleTemplate !== 'day_shift'
                    ) && (
                      <Typography variant="caption" color="primary" sx={{ display: 'block' }}>
                        {user.workSchedule.startTime}-{user.workSchedule.endTime}
                      </Typography>
                    )}
                  </Box>
                </Box>

                {/* Time Slots */}
                {hours.map((hour, hIdx) => {
                  // Check if user has custom work schedule for this time
                  const hasCustomSchedule = user.workSchedule && (
                    user.workSchedule.startTime !== '09:00' ||
                    user.workSchedule.endTime !== '17:30' ||
                    user.workSchedule.scheduleTemplate !== 'day_shift'
                  );

// Check if this hour falls within user's work schedule
const hourNum = parseInt(hour.split(':')[0], 10);
const startHour = user.workSchedule?.startTime ? parseInt(user.workSchedule.startTime.split(':')[0], 10) : 9;
const endHour = user.workSchedule?.endTime ? parseInt(user.workSchedule.endTime.split(':')[0], 10) : 17;
const isWorkingHour = hourNum >= startHour && hourNum < endHour;


                  return (
                    <Box
                      key={hIdx}
                      sx={{
                        width: SLOT_WIDTH,
                        height: ROW_HEIGHT,
                        borderRight: '1px solid #eee',
                        borderBottom: '1px solid #eee',
                        position: 'relative',
                        backgroundColor: hasCustomSchedule && isWorkingHour ? '#e3f2fd' : '#fafafa',
                        '&:hover .add-icon': { opacity: 1 },
                        '&:hover .schedule-info': { opacity: 1 }
                      }}
                    >
                      {/* Show schedule info if custom schedule exists and it's working hour */}
                      {hasCustomSchedule && isWorkingHour && (
                        <Box
                          className="schedule-info"
                          sx={{
                            position: 'absolute',
                            top: 2,
                            left: 2,
                            right: 2,
                            fontSize: '8px',
                            color: 'primary.main',
                            fontWeight: 600,
                            textAlign: 'center',
                            opacity: 0.7,
                            transition: 'opacity 0.3s',
                            lineHeight: 1,
                            overflow: 'hidden'
                          }}
                        >
                          {user.workSchedule.scheduleTemplate === 'night_shift' ? 'Night' : 'Day'}
                          <br />
                          {user.workSchedule.startTime}-{user.workSchedule.endTime}
                        </Box>
                      )}

                      <Tooltip title={hasCustomSchedule && isWorkingHour ? `${user.workSchedule.scheduleTemplate} (${user.workSchedule.startTime}-${user.workSchedule.endTime})` : "Add schedule"
                      }>
                        <IconButton
                          className="add-icon"
                          size="small"
                          sx={{
                            position: 'absolute',
                            bottom: 2,
                            right: 2,
                            opacity: 0,
                            transition: 'opacity 0.3s',
                            '& .MuiSvgIcon-root': {
                              fontSize: '12px'
                            }
                          }}
                          onClick={() => handleClick(user, hour)}
                        >
                          <AddCircleOutlineIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  );
                })}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>

      {/* Work Schedule Form */}
      <DayWorkScheduleForm
        open={open}
        onClose={handleClose}
        selectedUser={selected?.user}
        selectedDate={selected?.date}
        selectedHour={selected?.hour}
      />

      {/* Schedule Manager */}
      <ScheduleManager
        open={showScheduleManager}
        onClose={handleCloseScheduleManager}
        selectedUser={managerUser}
        selectedDate={currentDate.format('YYYY-MM-DD')}
      />
    </Box>
  );
};

DayWorkSchedule.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default DayWorkSchedule;
