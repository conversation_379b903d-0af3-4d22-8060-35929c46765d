import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Tooltip,
  Divider
} from '@mui/material';
import { Edit, Delete, Schedule, AccessTime, CalendarToday } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import { UserActions, GeneralActions } from 'slices/actions';
import { GeneralSelector } from 'selectors';
import WorkScheduleUtils from 'utils/workScheduleUtils';
import OptimizedScheduleForm from './OptimizedScheduleForm';
import dayjs from 'dayjs';

const ScheduleManager = ({ open, onClose, selectedUser, selectedDate }) => {
  const dispatch = useDispatch();
  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));
  const [showAdvancedForm, setShowAdvancedForm] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState(null);
  const [scheduleType, setScheduleType] = useState('daily');

  useEffect(() => {
    if (success) {
      toast.success(`Schedule updated successfully!`, {
        position: "top-right",
        autoClose: 3000,
        closeOnClick: true,
      });
      // Reset success state
      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));
    }
  }, [success, dispatch]);

  const userSchedules = selectedUser?.workSchedules || [];
  const defaultSchedule = selectedUser?.workSchedule;

  // Get effective schedule for the selected date
  const effectiveSchedule = selectedUser ? WorkScheduleUtils.getEffectiveSchedule(selectedUser, selectedDate) : null;

  const handleAddSchedule = (type) => {
    setScheduleType(type);
    setEditingSchedule(null);
    setShowAdvancedForm(true);
  };

  const handleEditSchedule = (schedule) => {
    setEditingSchedule(schedule);
    setScheduleType(schedule.type);
    setShowAdvancedForm(true);
  };

  const handleDeleteSchedule = (scheduleId) => {
    if (!selectedUser) {
      return;
    }

    const updatedSchedules = userSchedules.filter(s => s.id !== scheduleId);
    
    const params = {
      id: selectedUser._id,
      workSchedules: updatedSchedules
    };

    dispatch(UserActions.updateUser(params));
    toast.success('Schedule deleted successfully!');
  };

  const getScheduleTypeIcon = (type) => {
    switch (type) {
      case 'time_specific': return <AccessTime color="primary" />;
      case 'daily': return <CalendarToday color="secondary" />;
      case 'weekly': return <Schedule color="info" />;
      default: return <Schedule color="action" />;
    }
  };

  const getScheduleTypeColor = (type) => {
    switch (type) {
      case 'time_specific': return 'error';
      case 'daily': return 'warning';
      case 'weekly': return 'info';
      default: return 'default';
    }
  };

  const formatScheduleDisplay = (schedule) => {
    const startTime = schedule.startTime;
    const endTime = schedule.endTime;
    const template = schedule.scheduleTemplate === 'day_shift' ? 'Day' : 'Night';
    
    let dateInfo = '';
    if (schedule.type === 'time_specific' && schedule.specificDate) {
      dateInfo = ` on ${dayjs(schedule.specificDate).format('MMM D, YYYY')}`;
    } else if (schedule.type === 'daily' && schedule.specificDate) {
      dateInfo = ` on ${dayjs(schedule.specificDate).format('MMM D, YYYY')}`;
    } else if (schedule.type === 'weekly' && schedule.daysOfWeek) {
      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      const days = schedule.daysOfWeek.map(d => dayNames[d]).join(', ');
      dateInfo = ` on ${days}`;
    }

    return `${template} Shift: ${startTime}-${endTime}${dateInfo}`;
  };

  const isScheduleActive = (schedule) => {
    if (!selectedDate) {
      return false;
    }
    return WorkScheduleUtils.isScheduleApplicable(schedule, dayjs(selectedDate));
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Schedule />
            <Typography variant="h6">
              Schedule Manager - {selectedUser?.name}
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            Manage multiple work schedules with priority-based resolution
          </Typography>
        </DialogTitle>
        
        <DialogContent>
          {/* Current Effective Schedule */}
          {effectiveSchedule && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Current Effective Schedule for {selectedDate}:
              </Typography>
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>{effectiveSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift'}:</strong> {' '}
                  {effectiveSchedule.startTime} - {effectiveSchedule.endTime} ({effectiveSchedule.minimumHours}h)
                  {effectiveSchedule.type && (
                    <Chip 
                      label={effectiveSchedule.type.replace('_', ' ').toUpperCase()} 
                      size="small" 
                      color={getScheduleTypeColor(effectiveSchedule.type)}
                      sx={{ ml: 1 }}
                    />
                  )}
                </Typography>
              </Alert>
            </Box>
          )}

          {/* Add Schedule Buttons */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Add New Schedule:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<AccessTime />}
                onClick={() => handleAddSchedule('time_specific')}
              >
                Time-Specific
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={<CalendarToday />}
                onClick={() => handleAddSchedule('daily')}
              >
                Daily
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Schedule />}
                onClick={() => handleAddSchedule('weekly')}
              >
                Weekly
              </Button>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Default Schedule */}
          {defaultSchedule && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Default Schedule:
              </Typography>
              <ListItem sx={{ bgcolor: 'grey.50', borderRadius: 1 }}>
                <ListItemText
                  primary={formatScheduleDisplay(defaultSchedule)}
                  secondary="Base schedule used when no specific schedule applies"
                />
                <Chip label="DEFAULT" size="small" color="default" />
              </ListItem>
            </Box>
          )}

          {/* Specific Schedules */}
          <Typography variant="subtitle1" gutterBottom>
            Specific Schedules ({userSchedules.length}):
          </Typography>
          
          {userSchedules.length === 0 ? (
            <Alert severity="info">
              No specific schedules created. The default schedule will be used for all times.
            </Alert>
          ) : (
            <List>
              {userSchedules.sort((a, b) => (b.priority || 1) - (a.priority || 1)).map((schedule, index) => (
                <ListItem 
                  key={schedule.id || index}
                  sx={{ 
                    bgcolor: isScheduleActive(schedule) ? 'primary.light' : 'transparent',
                    borderRadius: 1,
                    mb: 1,
                    border: isScheduleActive(schedule) ? '1px solid' : 'none',
                    borderColor: 'primary.main'
                  }}
                >
                  <Box sx={{ mr: 1 }}>
                    {getScheduleTypeIcon(schedule.type)}
                  </Box>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {formatScheduleDisplay(schedule)}
                        <Chip 
                          label={`Priority ${schedule.priority || 1}`} 
                          size="small" 
                          color={getScheduleTypeColor(schedule.type)}
                        />
                        {isScheduleActive(schedule) && (
                          <Chip label="ACTIVE" size="small" color="primary" />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          {schedule.description || 'No description'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Effective: {dayjs(schedule.effectiveFrom).format('MMM D')} - {dayjs(schedule.effectiveTo).format('MMM D, YYYY')}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="Edit Schedule">
                      <IconButton 
                        edge="end" 
                        onClick={() => handleEditSchedule(schedule)}
                        sx={{ mr: 1 }}
                      >
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Schedule">
                      <IconButton 
                        edge="end" 
                        onClick={() => handleDeleteSchedule(schedule.id)}
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}

          {/* Schedule Priority Explanation */}
          <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
            <Typography variant="caption" color="info.dark">
              <strong>Schedule Priority:</strong> Time-Specific (4) → Daily (3) → Weekly (2) → Default (1). 
              Higher priority schedules override lower ones when multiple schedules apply to the same time.
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} color="secondary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Optimized Work Schedule Form */}
      <OptimizedScheduleForm
        open={showAdvancedForm}
        onClose={() => {
          setShowAdvancedForm(false);
          setEditingSchedule(null);
        }}
        selectedUser={selectedUser}
        selectedDate={selectedDate}
        scheduleType={scheduleType}
        editingSchedule={editingSchedule}
      />
    </>
  );
};

ScheduleManager.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedUser: PropTypes.object,
  selectedDate: PropTypes.string
};

export default ScheduleManager;
