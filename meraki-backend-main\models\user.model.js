'use strict';

const mongoose = require("mongoose");

const UserSchema = new mongoose.Schema({
    email: {
        type: String,
        text: true
    },
    password: String,
    name: {
        type: String,
        text: true
    },
    phone: String,
    avatar: {
        type: String,
        default: ""
    },
    country: Object,
    city: String,
    address: String,
    gender: String,
    birthday: String,
    description: String,
    department: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Department"
    },
    designation: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Designation"
    },
    role: Array,
    status: Number,
    lastActive: Date,
    deletedAt: Date,
    totalLeaves: Number,
    leaveTaken: {
        type: Number,
        default:0
    },
    availableLeaves: Number,
    paidLeaves:{
        type: Number,
        default: 0
    },
    unpaidLeaves: {
        type: Number,
        default: 0
    },
    otherLeaves:{
        type: Number,
        default: 0
    },

    // Work hours tracking
    workHours: {
        type: Number,
        default: 8.5
    },

    // Work Schedule Configuration
    workSchedule: {
        scheduleTemplate: {
            type: String,
            enum: ['day_shift', 'night_shift'],
            default: 'day_shift'
        },
        shiftStart: {
            type: Date,
            default: Date.now
        },
        shiftEnd: {
            type: Date,
            default: Date.now
        },
        startTime: {
            type: String,
            default: '09:00'
        },
        endTime: {
            type: String,
            default: '17:00'
        },
        minimumHours: {
            type: Number,
            default: 8.0
        }
    },

    // ✅ New: Dynamic Permissions
    permissions: [
        {
            feat: String,        // Feature key
            acts: [String]       // Allowed actions like ['read', 'update']
        }
    ]
}, { timestamps: true })

// Handle workSchedule migration when loading from database
UserSchema.pre('init', function(doc) {
    if (doc.workSchedule && typeof doc.workSchedule === 'string') {
        doc.workSchedule = {
            scheduleTemplate: 'day_shift',
            shiftStart: new Date(),
            shiftEnd: new Date(),
            startTime: '09:00',
            endTime: '17:00',
            minimumHours: 8.0
        };
    }
});

UserSchema.pre('save',function(next) {
    this.availableLeaves = this.totalLeaves
    console.log(" Availabel Leaves ",this.availableLeaves,this.totalLeaves)

    // Handle workSchedule migration - if it's a string, reset to default object
    if (typeof this.workSchedule === 'string') {
        this.workSchedule = {
            scheduleTemplate: 'day_shift',
            shiftStart: new Date(),
            shiftEnd: new Date(),
            startTime: '09:00',
            endTime: '17:00',
            minimumHours: 8.0
        };
    }

    next()
})
const User = mongoose.model("User", UserSchema)


module.exports = User;