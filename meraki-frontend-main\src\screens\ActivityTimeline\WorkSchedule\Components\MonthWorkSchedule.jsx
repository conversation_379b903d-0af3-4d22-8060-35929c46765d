import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Grid, IconButton
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { UserActions } from 'slices/actions';
import { UserSelector } from 'selectors';
import MonthWorkScheduleForm from './MonthWorkScheduleForm';

// Weekday headers
const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

// Generate actual calendar for current month
const generateCalendarDays = (year, month) => {
  const calendar = [];
  const date = new Date(year, month, 1);
  const firstDayIndex = date.getDay(); // 0 = Sunday, 6 = Saturday
  const totalDays = new Date(year, month + 1, 0).getDate(); // total days in month

  let currentDay = 1;
  for (let week = 0; week < 6; week++) {
    const weekDays = [];
    for (let day = 0; day < 7; day++) {
      if ((week === 0 && day < firstDayIndex) || currentDay > totalDays) {
        weekDays.push('');
      } else {
        weekDays.push(currentDay++);
      }
    }
    calendar.push(weekDays);
    if (currentDay > totalDays) { break } // Stop after finishing the month
  }
  return calendar;
};

const MonthWorkSchedule = ({ dateRange }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());

  // Get the current month from dateRange or default to current month
  const currentDate = dateRange?.startDate ? dayjs(dateRange.startDate) : dayjs();
  const year = currentDate.year();
  const month = currentDate.month(); // 0-based index

  const [open, setOpen] = useState(false);
  const [selectedData, setSelectedData] = useState(null);
  const calendar = generateCalendarDays(year, month);

  // Fetch users when component mounts
  useEffect(() => {
    dispatch(UserActions.getUsers());
  }, [dispatch]);

  const handleOpen = (day, user = null) => {
    if (day !== '') {
      const selectedDateFormatted = `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      setSelectedData({
        date: selectedDateFormatted,
        user: user || (users.length > 0 ? users[0] : null), // Default to first user if no specific user
        day: day
      });
      setOpen(true);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedData(null);
  };

  return (
    <Box p={2}>
      <Typography variant="h5" mb={2}>
        {currentDate.format('MMMM YYYY')} Work Schedule
      </Typography>

      {/* Header */}
      <Grid container spacing={0.5}>
        {daysOfWeek.map((day) => (
          <Grid key={day} item xs>
            <Typography align="center" fontWeight="bold">{day}</Typography>


          </Grid>
        ))}
      </Grid>

      {/* Calendar Grid */}
      {calendar.map((week, weekIdx) => (
        <Grid container spacing={0.5} key={weekIdx} mt={0.5}>
          {week.map((day, dayIdx) => (
            <Grid item xs key={dayIdx}>
              <Box
                sx={{
                  height: 80,
                  bgcolor: 'rgba(0,0,0,0.03)',
                  borderRadius: 1,
                  position: 'relative',
                  cursor: day !== '' ? 'pointer' : 'default',
                  '&:hover .add-icon': { opacity: 1 },
                }}
                onClick={() => handleOpen(day)}
              >
                <Typography p={1} variant="body2">{day}</Typography>
                {day !== '' && (
                  <IconButton
                    className="add-icon"
                    sx={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      opacity: 0,
                      transition: 'opacity 0.3s',
                    }}
                  >
                    <AddCircleOutlineIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      ))}

      {/* Work Schedule Form */}
      <MonthWorkScheduleForm
        open={open}
        onClose={handleClose}
        selectedUser={selectedData?.user}
        selectedDate={selectedData?.date}
      />
    </Box>
  );
};

MonthWorkSchedule.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default MonthWorkSchedule;
